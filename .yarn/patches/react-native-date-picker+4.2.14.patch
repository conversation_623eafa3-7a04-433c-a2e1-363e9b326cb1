diff --git a/ios/RNDatePicker/DatePicker.m b/ios/RNDatePicker/DatePicker.m
index 33323a6..0823642 100644
--- a/ios/RNDatePicker/DatePicker.m
+++ b/ios/RNDatePicker/DatePicker.m
@@ -49,6 +49,7 @@
 - (instancetype)initWithFrame:(CGRect)frame
 {
     if ((self = [super initWithFrame:frame])) {
+        [self addTarget:self action:@selector(handleDatePickerTap) forControlEvents:UIControlEventEditingDidBegin];
         [self addTarget:self action:@selector(didChange)
        forControlEvents:UIControlEventValueChanged];
         if(@available(iOS 13, *)) {
@@ -65,6 +66,10 @@
     return self;
 }

+- (void)handleDatePickerTap  {
+    [self resignFirstResponder];
+}
+
 - (void)setColor:(NSString *)hexColor {
     // Hex to int color
     unsigned intColor = 0;
