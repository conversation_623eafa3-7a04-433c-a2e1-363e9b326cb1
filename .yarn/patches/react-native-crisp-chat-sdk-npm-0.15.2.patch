diff --git a/android/.classpath b/android/.classpath
new file mode 100644
index 0000000000000000000000000000000000000000..bbe97e501d368d5d897c1c277d850a9d405188e7
--- /dev/null
+++ b/android/.classpath
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<classpath>
+	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17/"/>
+	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
+	<classpathentry kind="output" path="bin/default"/>
+</classpath>
diff --git a/android/.settings/org.eclipse.buildship.core.prefs b/android/.settings/org.eclipse.buildship.core.prefs
index 28c59ad20cc0c2abd4b732903e73f43be97f0c9c..e1b3d4dfe2491cf4a912cebc0b4f5486a18f0fbb 100644
--- a/android/.settings/org.eclipse.buildship.core.prefs
+++ b/android/.settings/org.eclipse.buildship.core.prefs
@@ -1,11 +1,11 @@
-arguments=--init-script /var/folders/02/17pns4kx4557339hxk2nhf2r0000gn/T/d146c9752a26f79b52047fb6dc6ed385d064e120494f96f08ca63a317c41f94c.gradle --init-script /var/folders/02/17pns4kx4557339hxk2nhf2r0000gn/T/52cde0cfcf3e28b8b7510e992210d9614505e0911af0c190bd590d7158574963.gradle
+arguments=--init-script /var/folders/db/21r4mhxs39z9kn734p5jb9yw0000gq/T/db3b08fc4a9ef609cb16b96b200fa13e563f396e9bb1ed0905fdab7bc3bc513b.gradle --init-script /var/folders/db/21r4mhxs39z9kn734p5jb9yw0000gq/T/52cde0cfcf3e28b8b7510e992210d9614505e0911af0c190bd590d7158574963.gradle
 auto.sync=false
 build.scans.enabled=false
 connection.gradle.distribution=GRADLE_DISTRIBUTION(WRAPPER)
 connection.project.dir=
 eclipse.preferences.version=1
 gradle.user.home=
-java.home=/Library/Java/JavaVirtualMachines/zulu-11.jdk/Contents/Home
+java.home=/Library/Java/JavaVirtualMachines/temurin-11.jdk/Contents/Home
 jvm.arguments=
 offline.mode=false
 override.workspace.settings=true
diff --git a/android/build.gradle b/android/build.gradle
index 0df1105998f7aa878c32374eca4098af916e5c8f..1571f8c5a7fc6cc25894454f71a435191f334fc6 100644
--- a/android/build.gradle
+++ b/android/build.gradle
@@ -8,7 +8,7 @@ buildscript {
   }

   dependencies {
-    classpath 'com.android.tools.build:gradle:3.2.1'
+    classpath 'com.android.tools.build:gradle:7.2.2'
     // noinspection DifferentKotlinGradleVersion
     classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
   }
@@ -38,7 +38,7 @@ android {

   buildTypes {
     release {
-      minifyEnabled false
+      minifyEnabled true
     }
   }
   lintOptions {
@@ -126,5 +126,5 @@ dependencies {
   // noinspection GradleDynamicVersion
   api 'com.facebook.react:react-native:+'
   implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
-  implementation 'im.crisp:crisp-sdk:2.0.5'
+  implementation 'im.crisp:crisp-sdk:2.0.1beta2'
 }
diff --git a/android/src/main/java/com/reactnativecrispchatsdk/CrispChatSdkModule.kt b/android/src/main/java/com/reactnativecrispchatsdk/CrispChatSdkModule.kt
index 64abf090452136227f084e14433883220e0a597b..24c9496bcf7548b24fdfa9d0149ca4a43aeae74f 100644
--- a/android/src/main/java/com/reactnativecrispchatsdk/CrispChatSdkModule.kt
+++ b/android/src/main/java/com/reactnativecrispchatsdk/CrispChatSdkModule.kt
@@ -1,108 +1,197 @@
 package com.reactnativecrispchatsdk

+import android.app.Activity
 import android.content.Intent
-import com.facebook.react.bridge.Promise
+import com.facebook.react.bridge.Callback
 import com.facebook.react.bridge.ReactApplicationContext
 import com.facebook.react.bridge.ReactContextBaseJavaModule
 import com.facebook.react.bridge.ReactMethod
-import im.crisp.client.external.ChatActivity
-import im.crisp.client.external.Crisp
-import im.crisp.client.external.data.SessionEvent
-import im.crisp.client.external.data.SessionEvent.Color
-
-
-class CrispChatSdkModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
-
-    override fun getName(): String {
-        return "CrispChatSdk"
+import im.crisp.client.ChatActivity
+import im.crisp.client.Crisp
+import im.crisp.client.data.SessionEvent
+import im.crisp.client.data.SessionEvent.Color
+import android.os.Handler
+import android.os.Looper
+import com.facebook.react.bridge.ActivityEventListener
+import com.facebook.react.bridge.Arguments
+import com.facebook.react.bridge.BaseActivityEventListener
+import com.facebook.react.bridge.WritableMap
+import com.facebook.react.modules.core.DeviceEventManagerModule
+import com.facebook.react.modules.core.DeviceEventManagerModule.RCTDeviceEventEmitter
+
+
+class CrispChatSdkModule(reactContext: ReactApplicationContext) :
+  ReactContextBaseJavaModule(reactContext) {
+
+  override fun getName(): String {
+    return "CrispChatSdk"
+  }
+
+  private var sessionId: String = ""
+  private lateinit var sessionHandler: Handler
+  private val CRISP_CHAT_CLOSED = 1
+
+
+  @ReactMethod
+  fun setTokenId(id: String) {
+    val context = reactApplicationContext
+    Crisp.setTokenID(context, id)
+  }
+
+  @ReactMethod
+  fun setUserEmail(email: String) {
+    Crisp.setUserEmail(email)
+  }
+
+  @ReactMethod
+  fun setUserNickname(name: String) {
+    Crisp.setUserNickname(name)
+  }
+
+  @ReactMethod
+  fun setUserPhone(phone: String) {
+    Crisp.setUserPhone(phone)
+  }
+
+  @ReactMethod
+  fun setUserAvatar(url: String) {
+    Crisp.setUserAvatar(url)
+  }
+
+  @ReactMethod
+  fun setSessionSegment(segment: String) {
+    Crisp.setSessionSegment(segment)
+  }
+
+  @ReactMethod
+  fun setSessionString(key: String, value: String) {
+    Crisp.setSessionString(key, value)
+  }
+
+  @ReactMethod
+  fun setSessionBool(key: String, value: Boolean) {
+    Crisp.setSessionBool(key, value)
+  }
+
+  @ReactMethod
+  fun setSessionInt(key: String, value: Int) {
+    Crisp.setSessionInt(key, value)
+  }
+
+  @ReactMethod
+  fun pushSessionEvent(name: String, color: Int) {
+    var sessionEventColor: Color = Color.BLACK
+
+    when (color) {
+      0 -> sessionEventColor = Color.RED
+      1 -> sessionEventColor = Color.ORANGE
+      2 -> sessionEventColor = Color.YELLOW
+      3 -> sessionEventColor = Color.GREEN
+      4 -> sessionEventColor = Color.BLUE
+      5 -> sessionEventColor = Color.PURPLE
+      6 -> sessionEventColor = Color.PINK
+      7 -> sessionEventColor = Color.BROWN
+      8 -> sessionEventColor = Color.GREY
+      9 -> sessionEventColor = Color.BLACK
     }

-    @ReactMethod
-    fun configure(websiteId: String) {
-        val context = reactApplicationContext
-        Crisp.configure(context, websiteId)
+    Crisp.pushSessionEvent(
+      SessionEvent(
+        name,
+        sessionEventColor
+      )
+    )
+  }
+
+  @ReactMethod
+  fun resetSession() {
+    val context = reactApplicationContext
+    this.sessionId = ""
+    Crisp.resetChatSession(context)
+  }
+
+  @ReactMethod
+  fun getSessionId(callback: Callback) {
+    val context = reactApplicationContext
+    callback(this.sessionId)
+  }
+
+  @ReactMethod
+  fun show(callback: Callback) {
+    val activity = currentActivity ?: return
+    val context = reactApplicationContext
+    val crispIntent = Intent(context, ChatActivity::class.java)
+    context.addActivityEventListener(mActivityEventListener)
+
+    if (callback != null) {
+      sessionHandler = startSessionInterval(context) { session ->
+        this.sessionId = sessionId
+        callback(session)
+      }
     }

-    @ReactMethod
-    fun setTokenId(id: String){
-        try {
-          Crisp.setTokenID(reactApplicationContext, id)
-        } catch(error : Exception) { }
-    }
+    activity.startActivityForResult(crispIntent, CRISP_CHAT_CLOSED)
+  }

-    @ReactMethod
-    fun setUserEmail(email: String) {
-        Crisp.setUserEmail(email)
-    }
-    @ReactMethod
-    fun setUserNickname(name: String) {
-        Crisp.setUserNickname(name)
-    }

-    @ReactMethod
-    fun setUserPhone(phone: String){
-        Crisp.setUserPhone(phone)
-    }
+  private val mActivityEventListener: ActivityEventListener = object : BaseActivityEventListener() {
+    override fun onActivityResult(
+      activity: Activity,
+      requestCode: Int,
+      resultCode: Int,
+      intent: Intent?
+    ) {
+      if (requestCode == CRISP_CHAT_CLOSED) {
+        reactApplicationContext
+          .getJSModule(RCTDeviceEventEmitter::class.java)
+          .emit(CrispChatEvent.CrispChatClosed.toString(), null)

-    @ReactMethod
-    fun setUserAvatar(url: String){
-        Crisp.setUserAvatar(url)
+      }
     }
+  }

-    @ReactMethod
-    fun setSessionSegment(segment: String){
-        Crisp.setSessionSegment(segment)
-    }
+//  context.addActivityEventListener(mActivityEventListener)

-    @ReactMethod
-    fun setSessionString(key: String, value: String){
-        Crisp.setSessionString(key, value)
-    }
+}

-    @ReactMethod
-    fun setSessionBool(key: String, value: Boolean){
-        Crisp.setSessionBool(key, value)
-    }

-    @ReactMethod
-    fun setSessionInt(key: String, value: Int){
-        Crisp.setSessionInt(key, value)
-    }
+fun startSessionInterval(context: ReactApplicationContext, callback: (String) -> Unit): Handler {
+  val interval = 1000L // 1s
+  val timeout = 10000L // 10s
+  val handler = Handler(Looper.getMainLooper())
+  var elapsedTime = 0L
+  var isCallbackInvoked = false
+
+  val runnable = object : Runnable {
+    override fun run() {
+      val session = Crisp.getSessionIdentifier(context)

-    @ReactMethod
-    fun pushSessionEvent(name: String, color: Int){
-      var sessionEventColor: Color = Color.BLACK
-
-      when(color){
-        0->sessionEventColor= Color.RED
-        1->sessionEventColor= Color.ORANGE
-        2->sessionEventColor= Color.YELLOW
-        3->sessionEventColor= Color.GREEN
-        4->sessionEventColor= Color.BLUE
-        5->sessionEventColor= Color.PURPLE
-        6->sessionEventColor= Color.PINK
-        7->sessionEventColor= Color.BROWN
-        8->sessionEventColor= Color.GREY
-        9->sessionEventColor= Color.BLACK
+      if (session != null && !isCallbackInvoked) {
+        // Có sessionId và chưa gọi callback trước đó
+        callback(session.toString())
+        isCallbackInvoked = true
+        cancelHandler(handler)
       }

-      Crisp.pushSessionEvent(SessionEvent(
-        name,
-        sessionEventColor
-      ))
+      if (elapsedTime < timeout && !isCallbackInvoked) {
+        elapsedTime += interval
+        handler.postDelayed(this, interval)
+      } else {
+        cancelHandler(handler)
+      }
     }
+  }

-    @ReactMethod
-    fun resetSession() {
-        val context = reactApplicationContext
-        Crisp.resetChatSession(context)
-    }
+  // Bắt đầu interval
+  handler.postDelayed(runnable, interval)

-    @ReactMethod
-    fun show() {
-        val context = reactApplicationContext
-        val crispIntent = Intent(context, ChatActivity::class.java)
-        crispIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
-        context.startActivity(crispIntent)
-    }
+  return handler
+}
+
+fun cancelHandler(handler: Handler) {
+  handler.removeCallbacksAndMessages(null)
+}
+
+enum class CrispChatEvent {
+  CrispChatClosed
 }
diff --git a/ios/CrispChatSdk-Bridging-Header.h b/ios/CrispChatSdk-Bridging-Header.h
index 586a9695d44fe032b3e73ae325db8b6ef13bef14..4f2c10c10bd34fd989045752f1df7266b98b13cb 100644
--- a/ios/CrispChatSdk-Bridging-Header.h
+++ b/ios/CrispChatSdk-Bridging-Header.h
@@ -1,2 +1,3 @@
 #import <React/RCTBridgeModule.h>
 #import <React/RCTUtils.h>
+#import <React/RCTEventEmitter.h>
diff --git a/ios/CrispChatSdk.m b/ios/CrispChatSdk.m
index 514b1d2d2e4e1745146cee42689337f024922c43..fdcbe15e04ecbf0fae3309416b9a72a00ef52dab 100644
--- a/ios/CrispChatSdk.m
+++ b/ios/CrispChatSdk.m
@@ -1,4 +1,5 @@
 #import <React/RCTBridgeModule.h>
+#import <React/RCTEventEmitter.h>

 @interface RCT_EXTERN_MODULE(CrispChatSdk, NSObject)

@@ -14,6 +15,7 @@ RCT_EXTERN_METHOD(setSessionBool:(NSString *)key value:(BOOL)value)
 RCT_EXTERN_METHOD(setSessionInt:(NSString *)key value:(int)value)
 RCT_EXTERN_METHOD(pushSessionEvent:(NSString *)eventName color:(NSInteger *)color)
 RCT_EXTERN_METHOD(resetSession)
-RCT_EXTERN_METHOD(show)
+RCT_EXTERN_METHOD(show: (RCTResponseSenderBlock)callback)
+RCT_EXTERN_METHOD(getSessionId: (RCTResponseSenderBlock)callback)

 @end
diff --git a/ios/CrispChatSdk.swift b/ios/CrispChatSdk.swift
index cc3571e01de9e72acc24bb7dac9c84124365f014..44cdfcfa35a52946209468f1fef8c4416b8f012f 100644
--- a/ios/CrispChatSdk.swift
+++ b/ios/CrispChatSdk.swift
@@ -1,7 +1,13 @@
 import Crisp

 @objc(CrispChatSdk)
-class CrispChatSdk: NSObject {
+class CrispChatSdk: RCTEventEmitter {
+    var sessionCallback: Crisp.CallbackToken?
+    var closeCallback: Crisp.CallbackToken?
+
+    override func supportedEvents() -> [String]! {
+        return [CrispChatEvent.CrispChatClosed.rawValue]
+    }

     @objc
     func configure(_ websiteId: String) {
@@ -63,7 +69,7 @@ class CrispChatSdk: NSObject {
     }

     @objc
-    func show() {
+    func show(_ callback: RCTResponseSenderBlock?) {
         DispatchQueue.main.async {
             var viewController = RCTPresentedViewController()

@@ -71,13 +77,48 @@ class CrispChatSdk: NSObject {
                 viewController = UIApplication.shared.windows.first?.rootViewController
             }

-            viewController?.present(ChatViewController(), animated: true)
+            if let viewController {
+                let chatViewController = ChatViewController()
+                chatViewController.modalTransitionStyle = .coverVertical
+                chatViewController.modalPresentationStyle = .fullScreen
+                viewController.present(chatViewController, animated: true)
+            }
+        }
+
+        if let sessionCallback = sessionCallback {
+            CrispSDK.removeCallback(token: sessionCallback)
+        }
+
+        if let closeCallback = closeCallback {
+            CrispSDK.removeCallback(token: closeCallback)
+        }
+        closeCallback = CrispSDK.addCallback(.chatClosed {
+            self.sendEvent(withName: CrispChatEvent.CrispChatClosed.rawValue, body: nil)
+        })
+
+        if let callback = callback {
+            sessionCallback = CrispSDK.addCallback(Callback.sessionLoaded { sessionId in
+                callback([sessionId])
+            })
         }
     }

     @objc
-    static func requiresMainQueueSetup() -> Bool {
+    func getSessionId(_ callback: RCTResponseSenderBlock) {
+        let id = CrispSDK.session.identifier ?? ""
+        callback([id])
+    }
+
+    // @objc
+    // static func requiresMainQueueSetup() -> Bool {
+    //     return true
+    // }
+
+    override static func requiresMainQueueSetup() -> Bool {
         return true
     }
+}

+enum CrispChatEvent: String {
+    case CrispChatClosed
 }
diff --git a/plugin/build/withCrispChat.js b/plugin/build/withCrispChat.js
index 56fb5b200ab6643de774ad1f2644f07adc1ce7e7..a8dda9d8a4f8671d8586deb9fb26d75a65466e34 100644
--- a/plugin/build/withCrispChat.js
+++ b/plugin/build/withCrispChat.js
@@ -54,7 +54,7 @@ public class MainApplication extends MultiDexApplication implements ReactApplica
     const cripsImport = /import im.crisp.client.external.Crisp;/g;
     if (!main.match(cripsImport)) {
         result = result.replace(/public class MainApplication extends MultiDexApplication implements ReactApplication {/, `import im.crisp.client.external.Crisp;
-
+
 public class MainApplication extends MultiDexApplication implements ReactApplication {`);
     }
     const cripWebsiteConfig = /Crisp\.configure\(getApplicationContext\(\),"(\w|\d|-)+"\);/g;
diff --git a/src/index.tsx b/src/index.tsx
index a860acf75092fedc3f8c2cc5c8f87c0a3d6734a0..5e0adf85c81450030c43e6fdd37b371dd76e7f59 100644
--- a/src/index.tsx
+++ b/src/index.tsx
@@ -1,5 +1,5 @@
 import * as React from 'react';
-import { NativeModules, View } from 'react-native';
+import { NativeEventEmitter, NativeModules, View } from 'react-native';

 export enum CrispSessionEventColors {
   RED = 0,
@@ -26,10 +26,14 @@ type CrispChatSdkType = {
   setSessionInt(key: string, value: number): () => void;
   pushSessionEvent(name: string, color: CrispSessionEventColors): () => void;
   resetSession(): () => void;
-  show(): () => void;
-  configure(websiteId: string): () => void;
+  getSessionId(onCallback: (string: string) => void): () => string;
+  show(onCallback?: (string: string) => void): () => string;
 };

+export enum CrispChatEvent {
+  CrispChatClosed = 'CrispChatClosed',
+}
+
 const CrispChatSdk = NativeModules.CrispChatSdk as CrispChatSdkType;

 const CrispChat: React.FC = () => {
@@ -37,62 +41,68 @@ const CrispChat: React.FC = () => {
     CrispChatSdk.show();
   }, []);

-  return <View />;
+  return <></>;
 };

 export default CrispChat;

-export const configure = (websiteId: string) => {
-  CrispChatSdk.configure(String(websiteId));
-};
-
 export const setTokenId = (id: string) => {
-  CrispChatSdk.setTokenId(String(id));
+  CrispChatSdk.setTokenId(id);
 };

 export const setUserEmail = (email: string) => {
-  CrispChatSdk.setUserEmail(String(email));
+  CrispChatSdk.setUserEmail(email);
 };

 export const setUserNickname = (name: string) => {
-  CrispChatSdk.setUserNickname(String(name));
+  CrispChatSdk.setUserNickname(name);
 };

 export const setUserPhone = (phone: string) => {
-  CrispChatSdk.setUserPhone(String(phone));
+  CrispChatSdk.setUserPhone(phone);
 };

 export const setUserAvatar = (url: string) => {
-  CrispChatSdk.setUserAvatar(String(url));
+  CrispChatSdk.setUserAvatar(url);
 };

 export const setSessionSegment = (segment: string) => {
-  CrispChatSdk.setSessionSegment(String(segment));
+  CrispChatSdk.setSessionSegment(segment);
 };

 export const setSessionString = (key: string, value: string) => {
-  CrispChatSdk.setSessionString(String(key), String(value));
+  CrispChatSdk.setSessionString(key, value);
 };

 export const setSessionBool = (key: string, value: boolean) => {
-  CrispChatSdk.setSessionBool(String(key), Boolean(value));
+  CrispChatSdk.setSessionBool(key, value);
 };

 export const setSessionInt = (key: string, value: number) => {
-  CrispChatSdk.setSessionInt(String(key), Number(value));
+  CrispChatSdk.setSessionInt(key, value);
 };

 export const pushSessionEvent = (
   name: string,
   color: CrispSessionEventColors
 ) => {
-  CrispChatSdk.pushSessionEvent(String(name), color);
+  CrispChatSdk.pushSessionEvent(name, color);
 };

 export const resetSession = () => {
   CrispChatSdk.resetSession();
 };

-export const show = () => {
-  CrispChatSdk.show();
+export const getSessionId = (onCallback: (string: string) => void) =>
+  CrispChatSdk.getSessionId(onCallback);
+
+export const show = (onCallback: (string: string) => void = () => {}) => {
+  CrispChatSdk.show(onCallback);
 };
+
+const CrispChatEmitter = new NativeEventEmitter(NativeModules.CrispChatSdk);
+
+export const addListener = (
+  event: CrispChatEvent,
+  callback: (data?: any) => void
+) => CrispChatEmitter.addListener(event, callback);
