diff --git a/src/CountDown/index.tsx b/src/CountDown/index.tsx
index 8384bb0..1a0d946 100644
--- a/src/CountDown/index.tsx
+++ b/src/CountDown/index.tsx
@@ -154,7 +154,7 @@ const CountdownComponent = React.forwardRef<any, CountDownProps>(
         } else {
           if (minute.current > 0) {
             return (
-              <Text style={[styles.text, textStyle, font()]}>{`${
+              <Text style={[styles.text, textStyle, font()]}>{`${minute.current.toString().length === 1 ? '0' : ''}${
                 minute.current
               }:${seconds.current.toString().length === 1 ? '0' : ''}${
                 seconds.current
@@ -164,7 +164,7 @@ const CountdownComponent = React.forwardRef<any, CountDownProps>(
             return (
               <Text
                 style={[styles.text, textStyle, font()]}
-              >{`${seconds.current}`}</Text>
+              >{`00:${seconds.current.toString().length === 1 ? '0' : ''}${seconds.current}`}</Text>
             );
           }
         }
diff --git a/src/Timer/index.tsx b/src/Timer/index.tsx
index 451b65d..0bb461e 100644
--- a/src/Timer/index.tsx
+++ b/src/Timer/index.tsx
@@ -171,7 +171,7 @@ const TimerComponent = React.forwardRef<any, TimerProps>((props, ref) => {
       } else {
         if (minute.current > 0) {
           return (
-            <Text style={[styles.text, textStyle, font()]}>{`${
+            <Text style={[styles.text, textStyle, font()]}>{`${minute.current.toString().length === 1 ? '0' : ''}${
               minute.current
             }:${seconds.current.toString().length === 1 ? '0' : ''}${
               seconds.current
@@ -181,7 +181,7 @@ const TimerComponent = React.forwardRef<any, TimerProps>((props, ref) => {
           return (
             <Text
               style={[styles.text, textStyle, font()]}
-            >{`${seconds.current}`}</Text>
+            >{`00:${seconds.current.toString().length === 1 ? '0' : ''}${seconds.current}`}</Text>
           );
         }
       }
