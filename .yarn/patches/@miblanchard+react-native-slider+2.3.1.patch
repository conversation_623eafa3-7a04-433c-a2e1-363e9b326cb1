diff --git a/lib/index.js b/lib/index.js
index 379639a..bfa2b99 100644
--- a/lib/index.js
+++ b/lib/index.js
@@ -442,6 +442,7 @@ export class Slider extends PureComponent {
         const minTrackWidth = _startFromZero
             ? Math.abs(_value) * sliderWidthCoefficient - thumbSize.width / 2
             : interpolatedTrackValues[0];
+        const maxTrackWidth = interpolatedTrackValues[1];
         const clearBorderRadius = {};
         if (_startFromZero && _value < 0 + step) {
             clearBorderRadius.borderBottomRightRadius = 0;
@@ -455,10 +456,10 @@ export class Slider extends PureComponent {
             position: 'absolute',
             left: interpolatedTrackValues.length === 1
                 ? new Animated.Value(startPositionOnTrack)
-                : Animated.add(minThumbValue, thumbSize.width / 2),
+                : Animated.add(minTrackWidth, thumbSize.width / 2),
             width: interpolatedTrackValues.length === 1
                 ? Animated.add(minTrackWidth, thumbSize.width / 2)
-                : Animated.add(Animated.multiply(minThumbValue, -1), maxThumbValue),
+                : Animated.add(Animated.multiply(minTrackWidth, -1), maxTrackWidth),
             backgroundColor: minimumTrackTintColor,
             ...valueVisibleStyle,
             ...clearBorderRadius,
