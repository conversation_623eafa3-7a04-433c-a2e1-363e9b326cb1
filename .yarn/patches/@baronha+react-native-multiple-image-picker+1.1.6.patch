diff --git a/android/build.gradle b/android/build.gradle
index 246072e..a9b6234 100644
--- a/android/build.gradle
+++ b/android/build.gradle
@@ -103,7 +103,6 @@ repositories {
           url androidSourcesDir.toString()
           name androidSourcesName
         }
-
         logger.info(":${project.name}:reactNativeAndroidRoot ${androidSourcesDir.canonicalPath}")
         found = true
       }
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/results.bin b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/results.bin
new file mode 100644
index 0000000..5ff383e
--- /dev/null
+++ b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/results.bin
@@ -0,0 +1 @@
+o/debug
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/BuildConfig.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/BuildConfig.dex
new file mode 100644
index 0000000..2f7b9ad
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/BuildConfig.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.dex
new file mode 100644
index 0000000..a8ec93b
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.dex
new file mode 100644
index 0000000..4390703
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine.dex
new file mode 100644
index 0000000..277fe62
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngine.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngineKt.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngineKt.dex
new file mode 100644
index 0000000..59137b6
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/CropEngineKt.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine$Companion.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine$Companion.dex
new file mode 100644
index 0000000..94d512f
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine$Companion.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.dex
new file mode 100644
index 0000000..de468ea
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine.dex
new file mode 100644
index 0000000..85c8e47
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/GlideEngine.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/ImageLoaderUtils.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/ImageLoaderUtils.dex
new file mode 100644
index 0000000..7e6c757
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/ImageLoaderUtils.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.dex
new file mode 100644
index 0000000..48be94b
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerModule.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerModule.dex
new file mode 100644
index 0000000..fcaadcd
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerModule.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.dex
new file mode 100644
index 0000000..307434d
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.dex
new file mode 100644
index 0000000..6ca399e
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.dex
new file mode 100644
index 0000000..a8f9334
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.dex b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.dex
new file mode 100644
index 0000000..5b8f1b2
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/debug_dex/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.dex differ
diff --git a/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/desugar_graph.bin b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/android/build/.transforms/0c4a7db4e8c02741d464aa649eef24c7/transformed/debug/desugar_graph.bin differ
diff --git a/android/build/.transforms/133c05cece0a8babe768f670447aee4f/results.bin b/android/build/.transforms/133c05cece0a8babe768f670447aee4f/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/android/build/.transforms/133c05cece0a8babe768f670447aee4f/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/android/build/.transforms/133c05cece0a8babe768f670447aee4f/transformed/classes/classes_dex/classes.dex b/android/build/.transforms/133c05cece0a8babe768f670447aee4f/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..8600c8f
Binary files /dev/null and b/android/build/.transforms/133c05cece0a8babe768f670447aee4f/transformed/classes/classes_dex/classes.dex differ
diff --git a/android/build/.transforms/1e6bb6e862675dcfa9b787081c4a9079/results.bin b/android/build/.transforms/1e6bb6e862675dcfa9b787081c4a9079/results.bin
new file mode 100644
index 0000000..4c288ce
--- /dev/null
+++ b/android/build/.transforms/1e6bb6e862675dcfa9b787081c4a9079/results.bin
@@ -0,0 +1 @@
+i/debug_dex
diff --git a/android/build/.transforms/2df9c23ab64a18475fc1203d3ae3d096/results.bin b/android/build/.transforms/2df9c23ab64a18475fc1203d3ae3d096/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/android/build/.transforms/2df9c23ab64a18475fc1203d3ae3d096/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/android/build/.transforms/3cbd972e2c94adc588f5e780e31e3c13/results.bin b/android/build/.transforms/3cbd972e2c94adc588f5e780e31e3c13/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/android/build/.transforms/3cbd972e2c94adc588f5e780e31e3c13/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/android/build/.transforms/92e66ee3be3a2a7810c17979c58eda65/results.bin b/android/build/.transforms/92e66ee3be3a2a7810c17979c58eda65/results.bin
new file mode 100644
index 0000000..e3f0ff0
--- /dev/null
+++ b/android/build/.transforms/92e66ee3be3a2a7810c17979c58eda65/results.bin
@@ -0,0 +1 @@
+i/classes_global-synthetics
diff --git a/android/build/generated/source/buildConfig/debug/com/reactnativemultipleimagepicker/BuildConfig.java b/android/build/generated/source/buildConfig/debug/com/reactnativemultipleimagepicker/BuildConfig.java
new file mode 100644
index 0000000..8bfb3ab
--- /dev/null
+++ b/android/build/generated/source/buildConfig/debug/com/reactnativemultipleimagepicker/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.reactnativemultipleimagepicker;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.reactnativemultipleimagepicker";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..00c76c9
--- /dev/null
+++ b/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,36 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="com.reactnativemultipleimagepicker" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
+    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
+    <uses-permission
+        android:name="android.permission.WRITE_MEDIA_STORAGE"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission
+        android:name="android.permission.WRITE_SETTINGS"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission
+        android:name="android.permission.MODIFY_AUDIO_SETTINGS"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission
+        android:name="android.permission.FOREGROUND_SERVICE"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission
+        android:name="android.permission.RECORD_AUDIO"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission android:name="android.permission.CAMERA" />
+    <uses-permission android:name="android.permission.VIBRATE" />
+    <uses-permission android:name="android.permission.BLUETOOTH" />
+
+    <!-- Android 13 -->
+    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
+    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
+    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
+
+    <application android:requestLegacyExternalStorage="true" />
+
+</manifest>
\ No newline at end of file
diff --git a/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..a46f6ea
--- /dev/null
+++ b/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.reactnativemultipleimagepicker",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9ae96ed
--- /dev/null
+++ b/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{"compiler-4.12.0.jar (com.github.bumptech.glide:compiler:4.12.0)":"INCREMENTAL_AP"}
\ No newline at end of file
diff --git a/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..8cb1484
Binary files /dev/null and b/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/android/build/intermediates/compile_r_class_jar/debug/R.jar b/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..1e93fa9
Binary files /dev/null and b/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/android/build/intermediates/compile_symbol_list/debug/R.txt b/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..3d9cf25
--- /dev/null
+++ b/android/build/intermediates/compile_symbol_list/debug/R.txt
@@ -0,0 +1,33 @@
+int color app_color_53575e 0x0
+int color app_color_9b 0x0
+int color app_color_black 0x0
+int color app_color_blue 0x0
+int color app_color_c51 0x0
+int color app_color_divider 0x0
+int color app_color_e0ff6100 0x0
+int color app_color_f6 0x0
+int color app_color_fa 0x0
+int color app_color_green 0x0
+int color app_color_grey 0x0
+int color app_color_pri 0x0
+int color app_color_red 0x0
+int color app_color_transparent 0x0
+int color app_color_white 0x0
+int color app_color_white_transparent 0x0
+int drawable button_selection 0x0
+int drawable checkbox_selector 0x0
+int drawable ic_checkmark 0x0
+int drawable ic_down 0x0
+int drawable num_oval_orange 0x0
+int drawable picture_new_item_select_bg 0x0
+int drawable picture_not_selected 0x0
+int drawable picture_selector 0x0
+int id picture_selector 0x0
+int style Base_Theme_NoActionBar 0x0
+int style PictureThemeDialogFragmentAnim 0x0
+int style PictureThemeDialogWindowStyle 0x0
+int style PictureThemeWindowStyle 0x0
+int style Picture_Theme_AlertDialog 0x0
+int style Picture_Theme_Dialog 0x0
+int style Picture_Theme_Dialog_AudioStyle 0x0
+int style Picture_Theme_Translucent 0x0
diff --git a/android/build/intermediates/compiled_local_resources/debug/out/drawable_button_selection.xml.flat b/android/build/intermediates/compiled_local_resources/debug/out/drawable_button_selection.xml.flat
new file mode 100644
index 0000000..5367f07
Binary files /dev/null and b/android/build/intermediates/compiled_local_resources/debug/out/drawable_button_selection.xml.flat differ
diff --git a/android/build/intermediates/compiled_local_resources/debug/out/drawable_checkbox_selector.xml.flat b/android/build/intermediates/compiled_local_resources/debug/out/drawable_checkbox_selector.xml.flat
new file mode 100644
index 0000000..050b1f7
Binary files /dev/null and b/android/build/intermediates/compiled_local_resources/debug/out/drawable_checkbox_selector.xml.flat differ
diff --git a/android/build/intermediates/compiled_local_resources/debug/out/drawable_ic_checkmark.xml.flat b/android/build/intermediates/compiled_local_resources/debug/out/drawable_ic_checkmark.xml.flat
new file mode 100644
index 0000000..8406714
Binary files /dev/null and b/android/build/intermediates/compiled_local_resources/debug/out/drawable_ic_checkmark.xml.flat differ
diff --git a/android/build/intermediates/compiled_local_resources/debug/out/drawable_ic_down.xml.flat b/android/build/intermediates/compiled_local_resources/debug/out/drawable_ic_down.xml.flat
new file mode 100644
index 0000000..437cfde
Binary files /dev/null and b/android/build/intermediates/compiled_local_resources/debug/out/drawable_ic_down.xml.flat differ
diff --git a/android/build/intermediates/compiled_local_resources/debug/out/drawable_num_oval_orange.xml.flat b/android/build/intermediates/compiled_local_resources/debug/out/drawable_num_oval_orange.xml.flat
new file mode 100644
index 0000000..7a2089c
Binary files /dev/null and b/android/build/intermediates/compiled_local_resources/debug/out/drawable_num_oval_orange.xml.flat differ
diff --git a/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_new_item_select_bg.xml.flat b/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_new_item_select_bg.xml.flat
new file mode 100644
index 0000000..378a1a4
Binary files /dev/null and b/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_new_item_select_bg.xml.flat differ
diff --git a/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_not_selected.xml.flat b/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_not_selected.xml.flat
new file mode 100644
index 0000000..41a1522
Binary files /dev/null and b/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_not_selected.xml.flat differ
diff --git a/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_selector.xml.flat b/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_selector.xml.flat
new file mode 100644
index 0000000..8882e47
Binary files /dev/null and b/android/build/intermediates/compiled_local_resources/debug/out/drawable_picture_selector.xml.flat differ
diff --git a/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..a74e1be
--- /dev/null
+++ b/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1,9 @@
+#Mon Jan 29 15:05:18 ICT 2024
+com.reactnativemultipleimagepicker.baronha_react-native-multiple-image-picker-main-6\:/drawable/button_selection.xml=/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/packaged_res/debug/drawable/button_selection.xml
+com.reactnativemultipleimagepicker.baronha_react-native-multiple-image-picker-main-6\:/drawable/checkbox_selector.xml=/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/packaged_res/debug/drawable/checkbox_selector.xml
+com.reactnativemultipleimagepicker.baronha_react-native-multiple-image-picker-main-6\:/drawable/ic_checkmark.xml=/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/packaged_res/debug/drawable/ic_checkmark.xml
+com.reactnativemultipleimagepicker.baronha_react-native-multiple-image-picker-main-6\:/drawable/ic_down.xml=/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/packaged_res/debug/drawable/ic_down.xml
+com.reactnativemultipleimagepicker.baronha_react-native-multiple-image-picker-main-6\:/drawable/num_oval_orange.xml=/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/packaged_res/debug/drawable/num_oval_orange.xml
+com.reactnativemultipleimagepicker.baronha_react-native-multiple-image-picker-main-6\:/drawable/picture_new_item_select_bg.xml=/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/packaged_res/debug/drawable/picture_new_item_select_bg.xml
+com.reactnativemultipleimagepicker.baronha_react-native-multiple-image-picker-main-6\:/drawable/picture_not_selected.xml=/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/packaged_res/debug/drawable/picture_not_selected.xml
+com.reactnativemultipleimagepicker.baronha_react-native-multiple-image-picker-main-6\:/drawable/picture_selector.xml=/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/packaged_res/debug/drawable/picture_selector.xml
diff --git a/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml b/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
new file mode 100644
index 0000000..a4b0ff4
--- /dev/null
+++ b/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
@@ -0,0 +1,68 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <color name="app_color_53575e">#53575e</color>
+    <color name="app_color_9b">#9b9b9b</color>
+    <color name="app_color_black">#000000</color>
+    <color name="app_color_blue">#7D7DFF</color>
+    <color name="app_color_c51">#f94c51</color>
+    <color name="app_color_divider">#B6B6B6</color>
+    <color name="app_color_e0ff6100">#E0FF6100</color>
+    <color name="app_color_f6">#f6f6f6</color>
+    <color name="app_color_fa">#fafafa</color>
+    <color name="app_color_green">#43c117</color>
+    <color name="app_color_grey">#393a3e</color>
+    <color name="app_color_pri">#FB9300</color>
+    <color name="app_color_red">#FF0000</color>
+    <color name="app_color_transparent">#00000000</color>
+    <color name="app_color_white">#FFFFFF</color>
+    <color name="app_color_white_transparent">#E0DBDBDB</color>
+    <style name="Base.Theme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/>
+    <style name="Picture.Theme.AlertDialog" parent="android:Theme.Dialog">
+        <item name="android:windowIsFloating">true</item>
+        <item name="android:windowIsTranslucent">false</item>
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowFullscreen">false</item>
+        <item name="android:windowBackground">@color/ps_color_transparent</item>
+        <item name="android:windowAnimationStyle">@null</item>
+        <item name="android:backgroundDimEnabled">false</item>
+        <item name="android:backgroundDimAmount">0.4</item>
+    </style>
+    <style name="Picture.Theme.Dialog" parent="@android:style/Theme.Dialog">
+
+
+        <item name="android:windowFrame">@android:color/transparent</item>
+
+        <item name="android:windowIsFloating">true</item>
+
+        <item name="android:windowIsTranslucent">false</item>
+
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowContentOverlay">@null</item>
+        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
+        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
+
+
+        <item name="android:windowBackground">@android:color/transparent</item>
+    </style>
+    <style name="Picture.Theme.Dialog.AudioStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_enter</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_exit</item>
+    </style>
+    <style name="Picture.Theme.Translucent" parent="Base.Theme.NoActionBar">
+        <item name="android:windowBackground">@color/ps_color_transparent</item>
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowIsTranslucent">true</item>
+    </style>
+    <style mce_bogus="1" name="PictureThemeDialogFragmentAnim" parent="android:Animation">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_up_in</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_down_out</item>
+    </style>
+    <style name="PictureThemeDialogWindowStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_modal_in</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_modal_out</item>
+    </style>
+    <style name="PictureThemeWindowStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_album_show</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_album_dismiss</item>
+    </style>
+</resources>
\ No newline at end of file
diff --git a/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..b8fb1de
--- /dev/null
+++ b/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,43 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res"><file name="num_oval_orange" path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/drawable/num_oval_orange.xml" qualifiers="" type="drawable"/><file name="ic_down" path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/drawable/ic_down.xml" qualifiers="" type="drawable"/><file name="button_selection" path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/drawable/button_selection.xml" qualifiers="" type="drawable"/><file name="picture_not_selected" path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/drawable/picture_not_selected.xml" qualifiers="" type="drawable"/><file name="picture_selector" path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/drawable/picture_selector.xml" qualifiers="" type="drawable"/><file name="checkbox_selector" path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/drawable/checkbox_selector.xml" qualifiers="" type="drawable"/><file name="ic_checkmark" path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/drawable/ic_checkmark.xml" qualifiers="" type="drawable"/><file name="picture_new_item_select_bg" path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/drawable/picture_new_item_select_bg.xml" qualifiers="" type="drawable"/><file path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/values/colors.xml" qualifiers=""><color name="app_color_grey">#393a3e</color><color name="app_color_black">#000000</color><color name="app_color_f6">#f6f6f6</color><color name="app_color_fa">#fafafa</color><color name="app_color_divider">#B6B6B6</color><color name="app_color_c51">#f94c51</color><color name="app_color_green">#43c117</color><color name="app_color_53575e">#53575e</color><color name="app_color_transparent">#00000000</color><color name="app_color_white">#FFFFFF</color><color name="app_color_white_transparent">#E0DBDBDB</color><color name="app_color_blue">#7D7DFF</color><color name="app_color_9b">#9b9b9b</color><color name="app_color_e0ff6100">#E0FF6100</color><color name="app_color_red">#FF0000</color><color name="app_color_pri">#FB9300</color></file><file path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/res/values/styles.xml" qualifiers=""><style name="Base.Theme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/><style name="Picture.Theme.Translucent" parent="Base.Theme.NoActionBar">
+        <item name="android:windowBackground">@color/ps_color_transparent</item>
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowIsTranslucent">true</item>
+    </style><style name="PictureThemeWindowStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_album_show</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_album_dismiss</item>
+    </style><style name="PictureThemeDialogWindowStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_modal_in</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_modal_out</item>
+    </style><style name="Picture.Theme.Dialog.AudioStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_enter</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_exit</item>
+    </style><style name="Picture.Theme.AlertDialog" parent="android:Theme.Dialog">
+        <item name="android:windowIsFloating">true</item>
+        <item name="android:windowIsTranslucent">false</item>
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowFullscreen">false</item>
+        <item name="android:windowBackground">@color/ps_color_transparent</item>
+        <item name="android:windowAnimationStyle">@null</item>
+        <item name="android:backgroundDimEnabled">false</item>
+        <item name="android:backgroundDimAmount">0.4</item>
+    </style><style mce_bogus="1" name="PictureThemeDialogFragmentAnim" parent="android:Animation">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_up_in</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_down_out</item>
+    </style><style name="Picture.Theme.Dialog" parent="@android:style/Theme.Dialog">
+
+
+        <item name="android:windowFrame">@android:color/transparent</item>
+
+        <item name="android:windowIsFloating">true</item>
+
+        <item name="android:windowIsTranslucent">false</item>
+
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowContentOverlay">@null</item>
+        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
+        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
+
+
+        <item name="android:windowBackground">@android:color/transparent</item>
+    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..d014eda
--- /dev/null
+++ b/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..7c61a5b
--- /dev/null
+++ b/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..8df439d
--- /dev/null
+++ b/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/build/intermediates/shader_assets/debug/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/android/build/intermediates/java_res/debug/out/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module b/android/build/intermediates/java_res/debug/out/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module
new file mode 100644
index 0000000..db7547a
Binary files /dev/null and b/android/build/intermediates/java_res/debug/out/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module differ
diff --git a/android/build/intermediates/javac/debug/classes/com/reactnativemultipleimagepicker/BuildConfig.class b/android/build/intermediates/javac/debug/classes/com/reactnativemultipleimagepicker/BuildConfig.class
new file mode 100644
index 0000000..5b06f0b
Binary files /dev/null and b/android/build/intermediates/javac/debug/classes/com/reactnativemultipleimagepicker/BuildConfig.class differ
diff --git a/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..49a6dca
--- /dev/null
+++ b/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,35 @@
+R_DEF: Internal format may change without notice
+local
+color app_color_53575e
+color app_color_9b
+color app_color_black
+color app_color_blue
+color app_color_c51
+color app_color_divider
+color app_color_e0ff6100
+color app_color_f6
+color app_color_fa
+color app_color_green
+color app_color_grey
+color app_color_pri
+color app_color_red
+color app_color_transparent
+color app_color_white
+color app_color_white_transparent
+drawable button_selection
+drawable checkbox_selector
+drawable ic_checkmark
+drawable ic_down
+drawable num_oval_orange
+drawable picture_new_item_select_bg
+drawable picture_not_selected
+drawable picture_selector
+id picture_selector
+style Base.Theme.NoActionBar
+style PictureThemeDialogFragmentAnim
+style PictureThemeDialogWindowStyle
+style PictureThemeWindowStyle
+style Picture.Theme.AlertDialog
+style Picture.Theme.Dialog
+style Picture.Theme.Dialog.AudioStyle
+style Picture.Theme.Translucent
diff --git a/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..3d05806
--- /dev/null
+++ b/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,69 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    xmlns:tools="http://schemas.android.com/tools"
+4    package="com.reactnativemultipleimagepicker" >
+5
+6    <uses-sdk android:minSdkVersion="21" />
+7
+8    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
+8-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:3:5-80
+8-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:3:22-77
+9    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
+9-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:4:5-81
+9-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:4:22-78
+10    <uses-permission
+10-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:5:5-114
+11        android:name="android.permission.WRITE_MEDIA_STORAGE"
+11-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:5:58-111
+12        tools:ignore="ProtectedPermissions" />
+12-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:5:22-57
+13    <uses-permission
+13-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:6:5-109
+14        android:name="android.permission.WRITE_SETTINGS"
+14-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:6:58-106
+15        tools:ignore="ProtectedPermissions" />
+15-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:6:22-57
+16    <uses-permission
+16-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:7:5-116
+17        android:name="android.permission.MODIFY_AUDIO_SETTINGS"
+17-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:7:58-113
+18        tools:ignore="ProtectedPermissions" />
+18-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:7:22-57
+19    <uses-permission
+19-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:8:5-113
+20        android:name="android.permission.FOREGROUND_SERVICE"
+20-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:8:58-110
+21        tools:ignore="ProtectedPermissions" />
+21-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:8:22-57
+22    <uses-permission
+22-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:9:5-107
+23        android:name="android.permission.RECORD_AUDIO"
+23-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:9:58-104
+24        tools:ignore="ProtectedPermissions" />
+24-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:9:22-57
+25    <uses-permission android:name="android.permission.CAMERA" />
+25-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:10:5-65
+25-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:10:22-62
+26    <uses-permission android:name="android.permission.VIBRATE" />
+26-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:11:5-66
+26-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:11:22-63
+27    <uses-permission android:name="android.permission.BLUETOOTH" />
+27-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:12:5-68
+27-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:12:22-65
+28
+29    <!-- Android 13 -->
+30    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
+30-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:15:5-76
+30-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:15:22-73
+31    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
+31-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:16:5-75
+31-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:16:22-72
+32    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
+32-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:17:5-75
+32-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:17:22-72
+33
+34    <application android:requestLegacyExternalStorage="true" />
+34-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:19:5-64
+34-->/Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:19:18-61
+35
+36</manifest>
diff --git a/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..00c76c9
--- /dev/null
+++ b/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,36 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="com.reactnativemultipleimagepicker" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
+    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
+    <uses-permission
+        android:name="android.permission.WRITE_MEDIA_STORAGE"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission
+        android:name="android.permission.WRITE_SETTINGS"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission
+        android:name="android.permission.MODIFY_AUDIO_SETTINGS"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission
+        android:name="android.permission.FOREGROUND_SERVICE"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission
+        android:name="android.permission.RECORD_AUDIO"
+        tools:ignore="ProtectedPermissions" />
+    <uses-permission android:name="android.permission.CAMERA" />
+    <uses-permission android:name="android.permission.VIBRATE" />
+    <uses-permission android:name="android.permission.BLUETOOTH" />
+
+    <!-- Android 13 -->
+    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
+    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
+    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
+
+    <application android:requestLegacyExternalStorage="true" />
+
+</manifest>
\ No newline at end of file
diff --git a/android/build/intermediates/navigation_json/debug/navigation.json b/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/android/build/intermediates/packaged_res/debug/drawable/button_selection.xml b/android/build/intermediates/packaged_res/debug/drawable/button_selection.xml
new file mode 100644
index 0000000..6cbee77
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/drawable/button_selection.xml
@@ -0,0 +1,10 @@
+<?xml version="1.0" encoding="utf-8"?>
+<shape xmlns:android="http://schemas.android.com/apk/res/android">
+    <solid android:color="@color/app_color_pri" />
+    <corners android:radius="5dp" />
+    <padding
+        android:bottom="5dp"
+        android:left="10dp"
+        android:right="10dp"
+        android:top="5dp" />
+</shape>
\ No newline at end of file
diff --git a/android/build/intermediates/packaged_res/debug/drawable/checkbox_selector.xml b/android/build/intermediates/packaged_res/debug/drawable/checkbox_selector.xml
new file mode 100644
index 0000000..9599e17
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/drawable/checkbox_selector.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<selector xmlns:android="http://schemas.android.com/apk/res/android">
+    <item android:drawable="@drawable/picture_not_selected" android:state_selected="false" />
+    <item android:drawable="@drawable/ic_checkmark" android:state_selected="true" />
+</selector>
diff --git a/android/build/intermediates/packaged_res/debug/drawable/ic_checkmark.xml b/android/build/intermediates/packaged_res/debug/drawable/ic_checkmark.xml
new file mode 100644
index 0000000..cda54cf
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/drawable/ic_checkmark.xml
@@ -0,0 +1,12 @@
+<vector xmlns:android="http://schemas.android.com/apk/res/android"
+    android:width="24dp"
+    android:height="24dp"
+    android:viewportWidth="24"
+    android:viewportHeight="24">
+  <path
+      android:pathData="M12,12m-12,0a12,12 0,1 1,24 0a12,12 0,1 1,-24 0"
+      android:fillColor="@color/app_color_pri"/>
+  <path
+      android:pathData="M15.7936,6.5936L10.1279,14.0879L7.6847,10.9253C7.4402,10.6113 7.081,10.4072 6.6861,10.358C6.2912,10.3088 5.8928,10.4185 5.5788,10.663C5.2648,10.9075 5.0607,11.2667 5.0115,11.6617C4.9623,12.0566 5.072,12.4549 5.3165,12.7689L8.9588,17.4304C9.0999,17.609 9.2799,17.7532 9.4851,17.8519C9.6902,17.9506 9.9152,18.0012 10.1429,18C10.3718,17.9994 10.5975,17.9465 10.8028,17.8451C11.008,17.7438 11.1873,17.5968 11.327,17.4154L18.1768,8.4222C18.4193,8.1042 18.5255,7.7029 18.4721,7.3065C18.4187,6.9102 18.21,6.5513 17.892,6.3088C17.574,6.0663 17.1727,5.9601 16.7763,6.0135C16.38,6.0669 16.0211,6.2756 15.7786,6.5936H15.7936Z"
+      android:fillColor="#ffffff"/>
+</vector>
diff --git a/android/build/intermediates/packaged_res/debug/drawable/ic_down.xml b/android/build/intermediates/packaged_res/debug/drawable/ic_down.xml
new file mode 100644
index 0000000..83b87b2
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/drawable/ic_down.xml
@@ -0,0 +1,9 @@
+<vector xmlns:android="http://schemas.android.com/apk/res/android"
+    android:width="24dp"
+    android:height="24dp"
+    android:viewportWidth="24"
+    android:viewportHeight="24">
+  <path
+      android:pathData="M12,16a1,1 0,0 1,-0.71 -0.29l-6,-6a1,1 0,0 1,1.42 -1.42l5.29,5.3 5.29,-5.29a1,1 0,0 1,1.41 1.41l-6,6a1,1 0,0 1,-0.7 0.29z"
+      android:fillColor="@color/app_color_grey"/>
+</vector>
diff --git a/android/build/intermediates/packaged_res/debug/drawable/num_oval_orange.xml b/android/build/intermediates/packaged_res/debug/drawable/num_oval_orange.xml
new file mode 100644
index 0000000..0348784
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/drawable/num_oval_orange.xml
@@ -0,0 +1,13 @@
+<?xml version="1.0" encoding="utf-8"?>
+<shape xmlns:android="http://schemas.android.com/apk/res/android"
+    android:shape="oval"
+    android:useLevel="false">
+
+    <solid android:color="@color/app_color_pri" />
+    <stroke
+        android:width="1.5dp"
+        android:color="@color/app_color_white" />
+    <size
+        android:width="24dp"
+        android:height="24dp" />
+</shape>
diff --git a/android/build/intermediates/packaged_res/debug/drawable/picture_new_item_select_bg.xml b/android/build/intermediates/packaged_res/debug/drawable/picture_new_item_select_bg.xml
new file mode 100644
index 0000000..5df58cc
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/drawable/picture_new_item_select_bg.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<selector xmlns:android="http://schemas.android.com/apk/res/android">
+
+    <item android:drawable="@color/app_color_white_transparent" android:state_pressed="true" />
+    <item android:drawable="@color/app_color_white_transparent" android:state_selected="true" />
+    <item android:drawable="@color/app_color_white" />
+</selector>
\ No newline at end of file
diff --git a/android/build/intermediates/packaged_res/debug/drawable/picture_not_selected.xml b/android/build/intermediates/packaged_res/debug/drawable/picture_not_selected.xml
new file mode 100644
index 0000000..b896af1
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/drawable/picture_not_selected.xml
@@ -0,0 +1,11 @@
+<?xml version="1.0" encoding="utf-8"?>
+<shape xmlns:android="http://schemas.android.com/apk/res/android"
+    android:shape="oval"
+    android:useLevel="false">
+   <stroke
+        android:width="2dp"
+        android:color="@color/app_color_9b" />
+    <size
+        android:width="24dp"
+        android:height="24dp" />
+</shape>
diff --git a/android/build/intermediates/packaged_res/debug/drawable/picture_selector.xml b/android/build/intermediates/packaged_res/debug/drawable/picture_selector.xml
new file mode 100644
index 0000000..37c4719
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/drawable/picture_selector.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<selector android:id="@+id/picture_selector" xmlns:android="http://schemas.android.com/apk/res/android">
+  <item android:drawable="@drawable/num_oval_orange" android:state_selected="true" />
+  <item android:drawable="@drawable/picture_not_selected" android:state_selected="false" />
+</selector>
diff --git a/android/build/intermediates/packaged_res/debug/values/values.xml b/android/build/intermediates/packaged_res/debug/values/values.xml
new file mode 100644
index 0000000..a4b0ff4
--- /dev/null
+++ b/android/build/intermediates/packaged_res/debug/values/values.xml
@@ -0,0 +1,68 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <color name="app_color_53575e">#53575e</color>
+    <color name="app_color_9b">#9b9b9b</color>
+    <color name="app_color_black">#000000</color>
+    <color name="app_color_blue">#7D7DFF</color>
+    <color name="app_color_c51">#f94c51</color>
+    <color name="app_color_divider">#B6B6B6</color>
+    <color name="app_color_e0ff6100">#E0FF6100</color>
+    <color name="app_color_f6">#f6f6f6</color>
+    <color name="app_color_fa">#fafafa</color>
+    <color name="app_color_green">#43c117</color>
+    <color name="app_color_grey">#393a3e</color>
+    <color name="app_color_pri">#FB9300</color>
+    <color name="app_color_red">#FF0000</color>
+    <color name="app_color_transparent">#00000000</color>
+    <color name="app_color_white">#FFFFFF</color>
+    <color name="app_color_white_transparent">#E0DBDBDB</color>
+    <style name="Base.Theme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/>
+    <style name="Picture.Theme.AlertDialog" parent="android:Theme.Dialog">
+        <item name="android:windowIsFloating">true</item>
+        <item name="android:windowIsTranslucent">false</item>
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowFullscreen">false</item>
+        <item name="android:windowBackground">@color/ps_color_transparent</item>
+        <item name="android:windowAnimationStyle">@null</item>
+        <item name="android:backgroundDimEnabled">false</item>
+        <item name="android:backgroundDimAmount">0.4</item>
+    </style>
+    <style name="Picture.Theme.Dialog" parent="@android:style/Theme.Dialog">
+
+
+        <item name="android:windowFrame">@android:color/transparent</item>
+
+        <item name="android:windowIsFloating">true</item>
+
+        <item name="android:windowIsTranslucent">false</item>
+
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowContentOverlay">@null</item>
+        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
+        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
+
+
+        <item name="android:windowBackground">@android:color/transparent</item>
+    </style>
+    <style name="Picture.Theme.Dialog.AudioStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_enter</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_exit</item>
+    </style>
+    <style name="Picture.Theme.Translucent" parent="Base.Theme.NoActionBar">
+        <item name="android:windowBackground">@color/ps_color_transparent</item>
+        <item name="android:windowNoTitle">true</item>
+        <item name="android:windowIsTranslucent">true</item>
+    </style>
+    <style mce_bogus="1" name="PictureThemeDialogFragmentAnim" parent="android:Animation">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_up_in</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_down_out</item>
+    </style>
+    <style name="PictureThemeDialogWindowStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_modal_in</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_modal_out</item>
+    </style>
+    <style name="PictureThemeWindowStyle">
+        <item name="android:windowEnterAnimation">@anim/ps_anim_album_show</item>
+        <item name="android:windowExitAnimation">@anim/ps_anim_album_dismiss</item>
+    </style>
+</resources>
\ No newline at end of file
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module b/android/build/intermediates/runtime_library_classes_dir/debug/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module
new file mode 100644
index 0000000..db7547a
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/BuildConfig.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/BuildConfig.class
new file mode 100644
index 0000000..5b06f0b
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/BuildConfig.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.class
new file mode 100644
index 0000000..38c8284
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.class
new file mode 100644
index 0000000..873f000
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine.class
new file mode 100644
index 0000000..9090708
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngine.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngineKt.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngineKt.class
new file mode 100644
index 0000000..b5c95f8
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/CropEngineKt.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine$Companion.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine$Companion.class
new file mode 100644
index 0000000..48a4f49
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine$Companion.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.class
new file mode 100644
index 0000000..e8ae728
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine.class
new file mode 100644
index 0000000..29f3676
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/GlideEngine.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/ImageLoaderUtils.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/ImageLoaderUtils.class
new file mode 100644
index 0000000..8c101f3
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/ImageLoaderUtils.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.class
new file mode 100644
index 0000000..faa3056
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule.class
new file mode 100644
index 0000000..5719537
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.class
new file mode 100644
index 0000000..5ea6d6e
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.class
new file mode 100644
index 0000000..8096c28
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.class
new file mode 100644
index 0000000..f1b7308
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.class differ
diff --git a/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.class b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.class
new file mode 100644
index 0000000..001a0d0
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.class differ
diff --git a/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..f06f8f5
Binary files /dev/null and b/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..60fe49e
--- /dev/null
+++ b/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1,34 @@
+com.reactnativemultipleimagepicker
+color app_color_53575e
+color app_color_9b
+color app_color_black
+color app_color_blue
+color app_color_c51
+color app_color_divider
+color app_color_e0ff6100
+color app_color_f6
+color app_color_fa
+color app_color_green
+color app_color_grey
+color app_color_pri
+color app_color_red
+color app_color_transparent
+color app_color_white
+color app_color_white_transparent
+drawable button_selection
+drawable checkbox_selector
+drawable ic_checkmark
+drawable ic_down
+drawable num_oval_orange
+drawable picture_new_item_select_bg
+drawable picture_not_selected
+drawable picture_selector
+id picture_selector
+style Base_Theme_NoActionBar
+style PictureThemeDialogFragmentAnim
+style PictureThemeDialogWindowStyle
+style PictureThemeWindowStyle
+style Picture_Theme_AlertDialog
+style Picture_Theme_Dialog
+style Picture_Theme_Dialog_AudioStyle
+style Picture_Theme_Translucent
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..aab3c1e
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..3c8edf3
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..bc74475
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..9e27f73
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..b058598
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..c9e1a58
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..2185025
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..4001616
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..53fddff
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..eb0b8a0
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..56876a5
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..ed108f3
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..55cc8de
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..4001616
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..53fddff
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..eb0b8a0
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..5755f39
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..ed108f3
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..111d151
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..c3e2af7
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..9e60f24
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..003bc0e
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..b332f78
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..9d156c7
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream
new file mode 100644
index 0000000..a7476e9
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len
new file mode 100644
index 0000000..7274ac0
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at
new file mode 100644
index 0000000..46d6744
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i
new file mode 100644
index 0000000..19f8a54
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..62dfcbd
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..ea2e6b5
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..0ac24d1
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..a541356
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..cf1aab7
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..d84a27e
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..8e183b9
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..0725e96
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..bc74475
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..9e27f73
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..4b72acc
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..9aa77cd
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab
new file mode 100644
index 0000000..9b06535
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream
new file mode 100644
index 0000000..0383b9d
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len
new file mode 100644
index 0000000..cf8a30a
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len
new file mode 100644
index 0000000..9e27f73
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at
new file mode 100644
index 0000000..05e0f06
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i
new file mode 100644
index 0000000..91918fc
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab
new file mode 100644
index 0000000..f961bf0
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream
new file mode 100644
index 0000000..b230f0a
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len
new file mode 100644
index 0000000..2c37006
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len
new file mode 100644
index 0000000..ec8f944
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at
new file mode 100644
index 0000000..b56d16d
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i
new file mode 100644
index 0000000..04c7359
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..888a92d
--- /dev/null
+++ b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+6
+0
\ No newline at end of file
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..f467935
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..0725e96
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..bc74475
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..9e27f73
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..87c4282
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..39e7d8e
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..2aacb87
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..3d5fe7a
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..fa902fa
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..9e27f73
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..27565e6
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i
new file mode 100644
index 0000000..b7dc911
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..bb87445
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..6ce3170
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..7b17051
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..043a3a0
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..c9835dd
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..9e84bfd
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/android/build/kotlin/compileDebugKotlin/cacheable/last-build.bin b/android/build/kotlin/compileDebugKotlin/cacheable/last-build.bin
new file mode 100644
index 0000000..4bf2a9d
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/cacheable/last-build.bin differ
diff --git a/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin b/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin
new file mode 100644
index 0000000..e4b4d49
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin differ
diff --git a/android/build/outputs/logs/manifest-merger-debug-report.txt b/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..91651f8
--- /dev/null
+++ b/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,85 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:1:1-20:12
+INJECTED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:1:1-20:12
+	package
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:2:2-46
+		INJECTED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml
+	xmlns:tools
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:1:70-116
+	xmlns:android
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:1:11-69
+uses-permission#android.permission.READ_EXTERNAL_STORAGE
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:3:5-80
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:3:22-77
+uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:4:5-81
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:4:22-78
+uses-permission#android.permission.WRITE_MEDIA_STORAGE
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:5:5-114
+	tools:ignore
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:5:22-57
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:5:58-111
+uses-permission#android.permission.WRITE_SETTINGS
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:6:5-109
+	tools:ignore
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:6:22-57
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:6:58-106
+uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:7:5-116
+	tools:ignore
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:7:22-57
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:7:58-113
+uses-permission#android.permission.FOREGROUND_SERVICE
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:8:5-113
+	tools:ignore
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:8:22-57
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:8:58-110
+uses-permission#android.permission.RECORD_AUDIO
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:9:5-107
+	tools:ignore
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:9:22-57
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:9:58-104
+uses-permission#android.permission.CAMERA
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:10:5-65
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:10:22-62
+uses-permission#android.permission.VIBRATE
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:11:5-66
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:11:22-63
+uses-permission#android.permission.BLUETOOTH
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:12:5-68
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:12:22-65
+uses-permission#android.permission.READ_MEDIA_IMAGES
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:15:5-76
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:15:22-73
+uses-permission#android.permission.READ_MEDIA_AUDIO
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:16:5-75
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:16:22-72
+uses-permission#android.permission.READ_MEDIA_VIDEO
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:17:5-75
+	android:name
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:17:22-72
+application
+ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:19:5-64
+	android:requestLegacyExternalStorage
+		ADDED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml:19:18-61
+uses-sdk
+INJECTED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/developer/khai-hoan/mobile/carz/liberty-carz-customer/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/AndroidManifest.xml
diff --git a/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..36e0c2e
Binary files /dev/null and b/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/android/build/tmp/kotlin-classes/debug/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module b/android/build/tmp/kotlin-classes/debug/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module
new file mode 100644
index 0000000..db7547a
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/META-INF/baronha_react-native-multiple-image-picker_debug.kotlin_module differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.class
new file mode 100644
index 0000000..38c8284
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1$loadImage$1.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.class
new file mode 100644
index 0000000..873f000
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine$onStartCrop$1.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine.class
new file mode 100644
index 0000000..9090708
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngine.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngineKt.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngineKt.class
new file mode 100644
index 0000000..b5c95f8
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/CropEngineKt.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine$Companion.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine$Companion.class
new file mode 100644
index 0000000..48a4f49
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine$Companion.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.class
new file mode 100644
index 0000000..e8ae728
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine$InstanceHolder.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine.class
new file mode 100644
index 0000000..29f3676
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/GlideEngine.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/ImageLoaderUtils.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/ImageLoaderUtils.class
new file mode 100644
index 0000000..8c101f3
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/ImageLoaderUtils.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.class
new file mode 100644
index 0000000..faa3056
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule$openPicker$1.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule.class
new file mode 100644
index 0000000..5719537
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerModule.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.class
new file mode 100644
index 0000000..5ea6d6e
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/MultipleImagePickerPackage.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.class
new file mode 100644
index 0000000..8096c28
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$Companion.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.class
new file mode 100644
index 0000000..f1b7308
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp$getResultCallbackListener$1.class differ
diff --git a/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.class b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.class
new file mode 100644
index 0000000..001a0d0
Binary files /dev/null and b/android/build/tmp/kotlin-classes/debug/com/reactnativemultipleimagepicker/PictureSelectorEngineImp.class differ
diff --git a/android/src/main/java/com/reactnativemultipleimagepicker/GlideEngine.kt b/android/src/main/java/com/reactnativemultipleimagepicker/GlideEngine.kt
index b57206a..fa0ac74 100644
--- a/android/src/main/java/com/reactnativemultipleimagepicker/GlideEngine.kt
+++ b/android/src/main/java/com/reactnativemultipleimagepicker/GlideEngine.kt
@@ -64,7 +64,7 @@ class GlideEngine private constructor() : ImageEngine {
       .override(180, 180)
       .sizeMultiplier(0.5f)
       .transform(CenterCrop(), RoundedCorners(8))
-      .placeholder(R.drawable.ps_image_placeholder)
+      .placeholder(com.luck.picture.lib.R.drawable.ps_image_placeholder)
       .into(imageView)
   }

@@ -83,7 +83,7 @@ class GlideEngine private constructor() : ImageEngine {
       .load(url)
       .override(200, 200)
       .centerCrop()
-      .placeholder(R.drawable.ps_image_placeholder)
+      .placeholder(com.luck.picture.lib.R.drawable.ps_image_placeholder)
       .into(imageView)
   }

diff --git a/android/src/main/java/com/reactnativemultipleimagepicker/MultipleImagePickerModule.kt b/android/src/main/java/com/reactnativemultipleimagepicker/MultipleImagePickerModule.kt
index 4df80bd..554945e 100644
--- a/android/src/main/java/com/reactnativemultipleimagepicker/MultipleImagePickerModule.kt
+++ b/android/src/main/java/com/reactnativemultipleimagepicker/MultipleImagePickerModule.kt
@@ -1,5 +1,6 @@
 package com.reactnativemultipleimagepicker

+import android.annotation.SuppressLint
 import android.content.Context
 import android.graphics.Bitmap
 import android.graphics.Color
@@ -133,6 +134,7 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         }
     }

+    @SuppressLint("ResourceAsColor")
     private fun setCropOptions(libOption: ReadableMap) {
         val options = UCrop.Options()
         val mainStyle: SelectMainStyle = style.selectMainStyle
@@ -159,8 +161,8 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :

         // ANIMATION SLIDE FROM BOTTOM
         val animationStyle = PictureWindowAnimationStyle()
-        animationStyle.setActivityEnterAnimation(R.anim.ps_anim_up_in)
-        animationStyle.setActivityExitAnimation(R.anim.ps_anim_down_out)
+        animationStyle.setActivityEnterAnimation(com.luck.picture.lib.R.anim.ps_anim_up_in)
+        animationStyle.setActivityExitAnimation(com.luck.picture.lib.R.anim.ps_anim_down_out)

         // TITLE BAR
         val titleBar = TitleBarStyle()
@@ -170,10 +172,15 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         titleBar.setHideCancelButton(true);
         titleBar.setAlbumTitleRelativeLeft(true);

-        titleBar.setTitleAlbumBackgroundResource(R.drawable.ps_album_bg);
-        titleBar.setTitleDrawableRightResource(R.drawable.ps_ic_grey_arrow);
-        titleBar.setPreviewTitleLeftBackResource(R.drawable.ps_ic_black_back);
-        titleBar.setTitleLeftBackResource(R.drawable.ps_ic_black_back);
+        titleBar.setTitleAlbumBackgroundResource(com.luck.picture.lib.R.drawable.ps_album_bg);
+//        titleBar.setTitleDrawableRightResource(R.drawable.ps_ic_grey_arrow);
+        titleBar.setTitleDrawableRightResource(com.luck.picture.lib.R.drawable.ps_ic_grey_arrow);
+
+//      titleBar.setPreviewTitleLeftBackResource(R.drawable.ps_ic_black_back);
+      titleBar.setPreviewTitleLeftBackResource(com.luck.picture.lib.R.drawable.ps_ic_black_back);
+      //      titleBar.setPreviewTitleLeftBackResource(R.drawable.ps_ic_black_back);
+
+      titleBar.setTitleLeftBackResource(com.luck.picture.lib.R.drawable.ps_ic_black_back);
         titleBar.setHideCancelButton(true)

         // BOTTOM BAR
@@ -183,12 +190,12 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         bottomBar.bottomPreviewSelectTextColor =
             ContextCompat.getColor(appContext, R.color.app_color_pri)
         bottomBar.bottomNarBarBackgroundColor =
-            ContextCompat.getColor(appContext, R.color.ps_color_white)
+            ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_white)
         bottomBar.bottomSelectNumResources = R.drawable.num_oval_orange
         bottomBar.bottomEditorTextColor =
-            ContextCompat.getColor(appContext, R.color.ps_color_53575e)
+            ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_53575e)
         bottomBar.bottomOriginalTextColor =
-            ContextCompat.getColor(appContext, R.color.ps_color_53575e)
+            ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_53575e)
         bottomBar.bottomPreviewNormalTextColor = R.color.app_color_53575e
         bottomBar.bottomPreviewNormalTextColor = R.color.app_color_black
         bottomBar.setCompleteCountTips(false)
@@ -202,7 +209,7 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         mainStyle.isSelectNumberStyle = true
         mainStyle.selectBackground = R.drawable.picture_selector
         mainStyle.mainListBackgroundColor =
-            ContextCompat.getColor(appContext, R.color.ps_color_white)
+            ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_white)
         mainStyle.previewSelectBackground =
             R.drawable.picture_selector

@@ -213,7 +220,7 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :


         mainStyle.selectNormalTextColor =
-            ContextCompat.getColor(appContext, R.color.ps_color_9b)
+            ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_9b)
         mainStyle.selectTextColor = primaryColor
         mainStyle.selectText = doneTitle

