name: Re-generate Language (yarn tx).

on:
  workflow_dispatch:

defaults:
  run:
    shell: bash -leo pipefail {0}

env:
  TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
  TELEGRAM_CHANNEL_ID: ${{ vars.TELEGRAM_CHANNEL_ID }}

jobs:
  generate-lang:
    name: Generate Language
    runs-on: [self-hosted, Linux, beaver]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.PAT_GITHUB }}
          ref: ${{ github.ref_name }}

      - name: Generate message
        run: |
          source .github/scripts/utils.sh
          echo "VALUE=$(get_message_information_for_generate_lang)" >> "$GITHUB_OUTPUT"
        timeout-minutes: 1
        id: message_information

      - name: send telegram message
        uses: PacificPromise/macos-telegram-action@main
        with:
          type: channel
          message: '🎬 - Start  regenerate Language: ${{ steps.message_information.outputs.VALUE }}'

      - name: Echo
        run: echo ${{ github.ref_name }}

      - name: Setup gem
        run: bundle install

      - name: Install libs
        run: source ~/.nvm/nvm.sh && nvm use && yarn install

      - name: Re-generate Language
        run:  source ~/.nvm/nvm.sh && nvm use && yarn tx

      - name: Push result
        run:  |
          source .github/scripts/utils.sh && config_github_token
          git add src/i18n/en/common.json
          git add src/i18n/km/common.json
          git add src/i18n/zh/common.json
          rm -rf .husky
          git commit -m 'chore: yarn tx - regenerate language.' || echo "Error"
          git push origin ${{ github.ref_name }} || echo "Error"

      - name: send telegram message
        uses: PacificPromise/macos-telegram-action@main
        with:
          type: channel
          message: '✅ - Finish regenerate Language: ${{ steps.message_information.outputs.VALUE }}'

      - name: Send message failure
        if: failure()
        uses: PacificPromise/macos-telegram-action@main
        with:
          type: channel
          message: '💥 - Failure regenerate Language: ${{ steps.message_information.outputs.VALUE }}'


