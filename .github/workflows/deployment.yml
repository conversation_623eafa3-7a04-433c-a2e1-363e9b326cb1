name: Deployment Customer

on:
  push:
    tags:
      - v[0-9]*\.[0-9]*\.[0-9]*\-dev\+[0-9]*
      - v[0-9]*\.[0-9]*\.[0-9]*\-stg\+[0-9]*
      - v[0-9]*\.[0-9]*\.[0-9]*\-prd\+[0-9]*

  workflow_dispatch:
    inputs:
      action_name:
        description: 'Action'
        default: 'Increment development environment'
        required: true
        type: choice
        options:
          - Increment development environment
          - Increment staging environment
          - Increment product environment

jobs:
  deployment:
    name: Deployment
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get stage key
        uses: dkershner6/switch-case-action@v1
        id: switch-case
        with:
          default: 'none'
          conditionals-with-values: |
            ${{ inputs.action_name == 'Increment development environment' }} => increment_tag dev
            ${{ inputs.action_name == 'Increment staging environment' }} => increment_tag stg
            ${{ inputs.action_name == 'Increment product environment' }} => increment_tag prd

      - name: Increment
        uses: PacificPromise/semantic-versioning-action@main
        if: ${{ steps.switch-case.outputs.value != 'none' }}
        with:
          script: ${{ steps.switch-case.outputs.value }}

      - name: Prepare
        id: parepare-step
        run: |
          source /dev/stdin <<<"$(curl -s https://gist.githubusercontent.com/tuanngocptn/c3868a202e37479e42471a9217d810d9/raw/lbt_mb_ci_utils.sh)"
          echo "tag-source=$(get_tag_source)" >> "$GITHUB_OUTPUT"
          echo "triggering-actor=${{ github.triggering_actor }}" >> "$GITHUB_OUTPUT"

      - name: Deploy Stage
        uses: fjogeleit/http-request-action@v1
        with:
          url: 'https://api.github.com/repos/LibertyTechnologyTeam/ppap/dispatches'
          method: 'POST'
          customHeaders: '{"Accept": "application/vnd.github+json", "Authorization": "Bearer ${{ secrets.GH_PAT }}", "X-GitHub-Api-Version": "2022-11-28"}'
          data: '{"event_type": "CarzCustomer-${{ steps.parepare-step.outputs.tag-source }}~${{ steps.parepare-step.outputs.triggering-actor }}", "client_payload": {"ref_name": "${{ steps.parepare-step.outputs.tag-source }}", "triggering_actor": "${{ steps.parepare-step.outputs.triggering-actor }}"}}'
