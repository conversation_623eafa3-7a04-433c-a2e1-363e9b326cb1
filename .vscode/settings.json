{"typescript.tsdk": "node_modules/typescript/lib", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "editor.formatOnSave": "explicit"}, "files.trimTrailingWhitespace": true, "workbench.colorCustomizations": {"activityBar.activeBorder": "#d73911", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#d73911", "activityBarBadge.foreground": "#e7e7e7", "statusBar.background": "#022A4C", "statusBar.foreground": "#e7e7e7", "statusBarItem.remoteBackground": "#022A4C", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#022A4C", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#022A4C99", "titleBar.inactiveForeground": "#e7e7e799"}, "cSpell.words": ["Actionsheet", "arrowdown", "<PERSON><PERSON>", "CARZ", "collapsable", "coolicon", "EADDRESS", "ellipsize", "favourite", "firestore", "flatlist", "<PERSON><PERSON>", "gorhom", "immer", "libertycarz", "mmkv", "modalfy", "Modalize", "Monobrow", "navigations", "netinfo", "noti", "positionslide", "pressable", "reduxjs", "RNFS", "softbreak", "sortby", "springify", "unsubcribe", "viewability", "Zoomable"], "editor.tabSize": 2, "java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx8G -Xms100m -Xlog:disable"}