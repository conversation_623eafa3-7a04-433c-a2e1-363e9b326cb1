{
  // Place your liberty-carz-customer workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  "liberty-screen-snippet": {
    "key": "reactNativeLibertyScreen",
    "prefix": "rnls",
    "body": [
      "import React from 'react'",
      "import {StyleSheet} from 'react-native'",
      "import {Container, Text} from '@liberty-ui-kit/components'",
      "import {ScreenProps} from '@app-types/NavigationType'",
      "import ScreensName from '@constant/screensName'",

      ""
      "type ${1:${TM_FILENAME_BASE}}Props = ScreenProps<ScreensName.${TM_FILENAME_BASE/(.*)$/${1:/upcase}/}>"

      "",
      "export default function ${1:${TM_FILENAME_BASE}}({navigation, route}: ${1:${TM_FILENAME_BASE}}Props) {",
      "  return (",
      "    <Container style={style.container}>",
      "      <Text>${1:first}</Text>",
      "    </Container>",
      "  )",
      "}",
      "",
      "const style = StyleSheet.create({"
      "  container: {"
      "    //"
      "  }"
      "})"
    ],
    "scope": "typescript,typescriptreact,javascript,javascriptreact"
  }
  "liberty-component-snippet": {
    "key": "reactNativeLibertyComponent",
    "prefix": "rnlc",
    "body": [
      "import React from 'react'",
      "import {StyleSheet, View} from 'react-native'",
      "import {Text} from '@liberty-ui-kit/components'",

      ""
      "type ${1:${TM_FILENAME_BASE}}Props = {"
      "  //"
      "}",
      ""

      "export function ${1:${TM_FILENAME_BASE}}(props: ${1:${TM_FILENAME_BASE}}Props) {",
      "  return (",
      "    <View style={style.container}>",
      "      <Text>${1:first}</Text>",
      "    </View>",
      "  )",
      "}",
      "",
      "const style = StyleSheet.create({"
      "  container: {"
      "     //"
      "  }"
      "})"
    ],
    "scope": "typescript,typescriptreact,javascript,javascriptreact"
  }
}
