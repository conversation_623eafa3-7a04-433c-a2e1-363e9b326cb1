{"name": "liberty", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios --simulator \"iPhone 15 Pro\"", "start": "react-native start", "run-app": "bash run_app.sh", "test": "jest", "lint": "eslint --ext .ts --ext .tsx ./src --fix", "reset": "rm -rf node_modules && npm cache clean --force && yarn install && watchman watch-del-all && rm -rf $TMPDIR/haste-map-* && rm -rf $TMPDIR/metro-cache", "prepare": "husky install", "tx": "node generate_locales.ts", "prettier": "prettier --write --cache --cache-location .cache/.prettier-cache src --log-level silent", "appicon:create": "npx icon-set-creator create", "clean:ios": "cd ios && rm -rf Pods && rm -rf Podfile.lock && pod install", "clean:android": "cd android && ./gradlew clean && ./gradlew --stop", "debug:android": "cd android && ./gradlew assembleDebug", "release:android": "cd android && ENVFILE=.env && ./gradlew assembleRelease", "release:play": "cd android && ./gradlew bundleRelease", "test:apk": "react-native run-android --variant=release", "icon": "bash .github/scripts/fetch_icon.sh", "deploy": "bash -c 'source /dev/stdin <<<\"$(curl -s https://raw.githubusercontent.com/tuanngocptn/semantic-versioning-action/v1/index.sh)\" && get_stage_prompt'", "commit-a": "git add . && git commit", "pod": "source .github/scripts/pod.sh && pod_install"}, "dependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@gorhom/bottom-sheet": "4.6.0", "@invertase/react-native-apple-authentication": "2.3.0", "@miblanchard/react-native-slider": "2.3.1", "@native-html/heuristic-table-plugin": "^0.7.0", "@native-html/iframe-plugin": "2.6.1", "@nnpmai/react-native-multiple-image-picker": "1.1.11", "@react-native-async-storage/async-storage": "^2.0.0", "@react-native-clipboard/clipboard": "^1.14.0", "@react-native-community/geolocation": "3.1.0", "@react-native-community/hooks": "3.0.0", "@react-native-community/netinfo": "^11.3.3", "@react-native-community/push-notification-ios": "1.11.0", "@react-native-firebase/analytics": "18.3.1", "@react-native-firebase/app": "18.3.1", "@react-native-firebase/auth": "18.3.1", "@react-native-firebase/firestore": "18.3.1", "@react-native-firebase/messaging": "18.3.1", "@react-native-google-signin/google-signin": "13.1.0", "@react-navigation/bottom-tabs": "6.5.11", "@react-navigation/material-top-tabs": "6.6.5", "@react-navigation/native": "6.1.9", "@react-navigation/native-stack": "6.9.17", "@reduxjs/toolkit": "1.9.7", "@shopify/flash-list": "1.6.3", "axios": "1.6.5", "carz-core-sdk": "git+ssh://**************:LibertyCarz/ts-carz-sdk-core-client.git#main", "dayjs": "1.11.9", "formik": "2.4.5", "i18n-js": "3.9.2", "immer": "10.0.3", "lodash": "4.17.21", "lodash-contrib": "4.1200.1", "lottie-react-native": "6.7.2", "react": "18.3.1", "react-hook-form": "7.44.0", "react-icomoon": "2.5.7", "react-native": "0.75.4", "react-native-appsflyer": "6.12.2", "react-native-awesome-gallery": "^0.4.0", "react-native-background-timer": "2.4.1", "react-native-branch": "6.1.0", "react-native-calendars": "1.1303.0", "react-native-compressor": "^1.8.24", "react-native-confirmation-code-field": "7.3.2", "react-native-crisp-chat-sdk": "https://github.com/baronha/react-native-crisp-chat-sdk", "react-native-date-picker": "4.2.14", "react-native-device-info": "10.12.0", "react-native-dropdownalert": "4.5.1", "react-native-easing-gradient": "^1.1.1", "react-native-element-dropdown": "2.10.1", "react-native-element-timer": "2.1.2", "react-native-error-boundary": "1.2.4", "react-native-fast-image": "8.6.3", "react-native-fbsdk-next": "12.1.2", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.14.1", "react-native-gifted-chat": "2.4.0", "react-native-haptic-feedback": "2.2.0", "react-native-json-tree": "1.3.0", "react-native-keyboard-controller": "1.14.4", "react-native-linear-gradient": "2.8.3", "react-native-markdown-display": "7.0.0-alpha.2", "react-native-mmkv": "2.11.0", "react-native-modalfy": "3.5.2", "react-native-pager-view": "6.5.0", "react-native-permissions": "3.10.1", "react-native-push-notification": "8.1.1", "react-native-qrcode-svg": "6.2.0", "react-native-reanimated": "3.15.3", "react-native-reanimated-carousel": "3.5.1", "react-native-redash": "18.1.3", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "^3.34.0", "react-native-share": "^11.0.3", "react-native-simple-heic2jpg": "^0.1.1", "react-native-splash-screen": "3.3.0", "react-native-static-safe-area-insets": "https://github.com/mainguyen12/react-native-static-safe-area-insets.git#62d76a2acdb572dd6aa968d51c701a510c858d7e", "react-native-step-indicator": "1.0.3", "react-native-svg": "^15.6.0", "react-native-tab-view": "3.5.2", "react-native-uuid": "2.0.1", "react-native-vector-icons": "10.0.3", "react-native-video": "v6.0.0-beta.5", "react-native-vision-camera": "4.5.0", "react-native-webview": "^13.12.2", "react-redux": "8.1.3", "redux": "4.2.0", "redux-saga": "1.2.3", "rn-fetch-blob": "0.12.0", "rn-placeholder": "3.0.3", "shortid": "2.2.16", "sprintf-js": "1.1.3", "use-immer": "0.9.0", "xdate": "0.8.2", "yup": "1.3.3"}, "devDependencies": {"@babel/core": "7.23.7", "@babel/plugin-transform-private-methods": "^7.25.9", "@babel/preset-env": "7.23.8", "@babel/runtime": "7.23.8", "@commitlint/cli": "18.5.0", "@commitlint/config-conventional": "17.8.1", "@react-native/babel-preset": "^0.76.2", "@react-native/eslint-config": "^0.76.2", "@react-native/metro-config": "^0.75.0", "@react-navigation/devtools": "6.0.20", "@tsconfig/react-native": "3.0.3", "@types/i18n-js": "3.8.9", "@types/lodash": "4.14.202", "@types/metro-config": "0.76.3", "@types/react": "18.2.6", "@types/react-native-video": "5.0.19", "@types/react-test-renderer": "18.0.7", "@types/sprintf-js": "1.1.4", "@typescript-eslint/eslint-plugin": "6.19.0", "@typescript-eslint/parser": "6.19.0", "babel-jest": "29.7.0", "babel-plugin-module-resolver": "5.0.0", "commitizen": "4.3.0", "editorconfig": "2.0.0", "eslint": "8.48.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "17.1.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-n": "16.6.2", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-promise": "6.1.1", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-native": "4.1.0", "eslint-plugin-unused-imports": "3.0.0", "husky": "8.0.3", "icon-set-creator": "1.2.6", "jest": "29.7.0", "lint-staged": "13.3.0", "prettier": "3.2.4", "react-test-renderer": "^18.2.0", "reactotron-react-native": "^5.1.12", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"]}, "packageManager": "yarn@3.6.4", "resolutions": {"react-native-date-picker@4.2.14": "patch:react-native-date-picker@npm:4.2.14#./.yarn/patches/react-native-date-picker+4.2.14.patch", "react-native-element-timer@2.1.2": "patch:react-native-element-timer@npm:2.1.2#./.yarn/patches/react-native-element-timer+2.1.2.patch", "@miblanchard/react-native-slider@2.3.1": "patch:@miblanchard/react-native-slider@npm:2.3.1#./.yarn/patches/@miblanchard+react-native-slider+2.3.1.patch", "react-native-modalfy@3.5.2": "patch:react-native-modalfy@npm%3A3.5.2#./.yarn/patches/react-native-modalfy-npm-3.5.2-464019615a.patch"}}