import {Container, Image, ScalableImage, Text} from '@liberty-ui-kit/components'
import React from 'react'
import {ScrollView, StyleSheet} from 'react-native'
import {fontSize, screenSize, space} from '@liberty-ui-kit/theme'

const DSImage = () => {
  return (
    <Container style={style.container}>
      <ScrollView>
        <Text style={style.title}>Image:</Text>
        <Image
          style={style.default}
          source={
            'https://images.unsplash.com/photo-1697128439428-a68faa7f2537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHw1MHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=60'
          }
        />
        <Text style={style.title}>ScalableImage:</Text>
        <ScalableImage
          imageWidth={screenSize.width}
          source={
            'https://images.unsplash.com/photo-1697128439428-a68faa7f2537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHw1MHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=60'
          }
        />
        <Text style={style.title}>Error:</Text>
        <Image style={style.error} />
      </ScrollView>
    </Container>
  )
}

export default DSImage

const style = StyleSheet.create({
  container: {},
  title: {
    padding: space.md,
    fontWeight: 'bold',
    fontSize: fontSize['3xl'],
  },
  default: {
    width: screenSize.width,
    height: screenSize.width / 2,
  },
  error: {
    width: screenSize.width,
    height: screenSize.width,
  },
})
