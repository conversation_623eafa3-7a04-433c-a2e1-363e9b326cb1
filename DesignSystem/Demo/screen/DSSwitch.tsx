import {Container} from '@liberty-ui-kit/components'
import React from 'react'
import {StyleSheet} from 'react-native'
import {defaultColor, primary, space} from '@liberty-ui-kit/theme'
import Switch from '@liberty-ui-kit/components/Switch'

const DSSwitch = () => {
  return (
    <Container style={style.container}>
      <Switch activeColor={primary[50]} inActiveColor={defaultColor[400]} />
    </Container>
  )
}

export default DSSwitch

const style = StyleSheet.create({
  container: {
    padding: space.md,
  },
})
