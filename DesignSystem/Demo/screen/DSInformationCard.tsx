import {
  Badge,
  Card,
  Container,
  HStack,
  List,
  NavigationBar,
  Text,
} from '@liberty-ui-kit/components'
import React from 'react'
import {StyleSheet, Text as RNText, View} from 'react-native'
import {colors, fontSize, iconSize, space} from '@liberty-ui-kit/theme'
import {useTheme} from '@liberty-ui-kit/hook'

const DSInformationCard = () => {
  const renderCard = () => {
    return (
      <Card style={style.card}>
        <Card.Header style={style.header}>
          <View>
            <Text style={style.title}>Period: 07/2023</Text>
            <Text muted>
              Date: <Text>22/04/2023</Text>
            </Text>
          </View>
          <Badge size="sm" status="success">
            Paid
          </Badge>
        </Card.Header>
        <HStack style={style.bottom}>
          <Text muted>
            Qty: <Text>4 products</Text>
          </Text>
          <Text muted>
            Total: <Text>$65,000</Text>
          </Text>
        </HStack>
      </Card>
    )
  }
  return (
    <Container style={style.container}>
      <NavigationBar title="Payment" />
      <Container level={'2'}>
        <List data={data} renderItem={renderCard} estimatedItemSize={104} />
      </Container>
    </Container>
  )
}

export default DSInformationCard

const style = StyleSheet.create({
  container: {},
  card: {
    marginHorizontal: space.md,
    marginTop: space.md,
  },
  header: {
    alignItems: 'flex-start',
  },
  title: {
    fontWeight: 'bold',
    color: colors.primary,
  },

  bottom: {
    justifyContent: 'space-between',
  },
})

const data = Array(1000).fill(0)
