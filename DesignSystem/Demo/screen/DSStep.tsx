import {
  But<PERSON>,
  Container,
  <PERSON><PERSON><PERSON><PERSON>,
  StepIndicator,
} from '@liberty-ui-kit/components'
import {space} from '@liberty-ui-kit/theme'
import React, {useState} from 'react'
import {StyleSheet} from 'react-native'

const data = ['Services', 'Booking', 'Finish']

const DSStep = () => {
  const [step, setStep] = useState(0)

  const onNext = () => {
    if (step < data.length - 1) setStep(step + 1)
  }

  const onPrev = () => {
    if (step !== 0) setStep(step - 1)
  }

  return (
    <Container style={style.container}>
      <StepIndicator currentIndex={step} data={data} />
      <HStack style={style.row}>
        <Button type="outline" style={style.buttonPrev} onPress={onPrev}>
          Previous
        </Button>
        <Button style={style.button} onPress={onNext}>
          Next
        </Button>
      </HStack>
    </Container>
  )
}

export default DSStep

const style = StyleSheet.create({
  container: {
    padding: space.md,
  },
  button: {
    flex: 1,
    marginLeft: space.sm,
  },
  buttonPrev: {},
  row: {
    marginTop: space.md,
  },
})
