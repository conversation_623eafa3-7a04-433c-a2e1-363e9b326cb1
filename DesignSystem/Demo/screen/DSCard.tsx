import React, {useState} from 'react'
import {FlatList, StyleSheet, View} from 'react-native'
import {colors, defaultColor, space, trueGray} from '@liberty-ui-kit/theme'
import {Button} from '@liberty-ui-kit/components'
import {CarCard} from '@liberty-ui-kit/components/CarCard'
import {CAR_TYPE, CarType} from '@stores/slices/home/<USER>'
import {GridCarCard} from '@liberty-ui-kit/components/GridCarCard'
import {BookingCarCard} from '@liberty-ui-kit/components/BookingCarCard'
import {CarSelectionCard} from '@liberty-ui-kit/components/CarSelectionCard'

const EXAMPLE_CAR = {
  name: 'Chervolet colorado dioe xs 2022 colorado Chervolet colorado dioe xs 2022 colorado',
  type: CAR_TYPE.RENT,
  price: '2000',
  description: 'Lorem ipsum dolor sit Lorem ipsum dolor sit',
} as CarType

const DSCard = () => {
  const [type, setType] = useState(1)
  const [isGridView, setGridView] = useState(false)
  const data = [1, 2, 3, 4, 5, 6, 7, 8]

  const renderCars = ({item, index}: {item: number; index: number}) => {
    return (
      <CarCard
        key={`car-${index}`}
        isPromo
        isVerified
        isQualified
        discount={20}
        numberLikes={3339}
        numberViews={7779}
        address="Phnom penh"
        style={style.card}
        car={EXAMPLE_CAR}
      />
    )
  }

  const renderGridCars = ({item, index}: {item: number; index: number}) => {
    return (
      <GridCarCard
        key={`car-grid-${index}`}
        isPromo
        isVerified
        isQualified
        discount={20}
        numberLikes={3339}
        numberViews={7779}
        address="Phnom penh"
        style={style.gridCard}
        car={EXAMPLE_CAR}
      />
    )
  }

  const renderBookingCars = ({item, index}: {item: number; index: number}) => {
    return (
      <BookingCarCard
        key={`booking-car-${index}`}
        style={style.card}
        car={EXAMPLE_CAR}
      />
    )
  }

  const renderCarSelectionCards = ({
    item,
    index,
  }: {
    item: number
    index: number
  }) => {
    return (
      <CarSelectionCard
        key={`car-selection-${index}`}
        isPromo
        isVerified
        isQualified
        discount={20}
        numberLikes={3339}
        numberViews={7779}
        style={style.selectionCards}
        car={EXAMPLE_CAR}
      />
    )
  }

  const chooseType = (type: number) => () => {
    setType(type)
  }

  const checkType = (buttonType: number) => {
    return buttonType === type ? 'solid' : 'outline'
  }

  const renderSeparate = ({item, index}: {item: number; index: number}) => {
    return <View style={style.separate} />
  }

  return (
    <View
      style={[
        style.container,
        {backgroundColor: type === 1 ? defaultColor[100] : colors.white},
      ]}
    >
      <View>
        <Button
          style={style.button}
          type={checkType(1)}
          onPress={chooseType(1)}
        >
          {'Car Card'}
        </Button>
        <Button
          style={style.button}
          type={checkType(2)}
          onPress={chooseType(2)}
        >
          {'Booking Car Card'}
        </Button>
        <Button
          style={style.button}
          type={checkType(3)}
          onPress={chooseType(3)}
        >
          {'Car Selection Card'}
        </Button>
        {type === 1 && (
          <Button
            style={[
              style.button,
              {width: 100, height: 40, alignSelf: 'flex-end'},
            ]}
            type={isGridView ? 'solid' : 'outline'}
            onPress={() => {
              setGridView(!isGridView)
            }}
          >
            {isGridView ? 'Grid' : 'List'}
          </Button>
        )}
      </View>
      {type === 1 && !isGridView && (
        <FlatList
          data={data}
          renderItem={renderCars}
          style={style.listCarsContainer}
        />
      )}
      {type === 1 && isGridView && (
        <FlatList
          numColumns={2}
          data={data}
          renderItem={renderGridCars}
          style={style.gridCardsWrap}
        />
      )}
      {type === 2 && (
        <FlatList
          data={data}
          renderItem={renderBookingCars}
          ItemSeparatorComponent={renderSeparate}
        />
      )}
      {type === 3 && (
        <FlatList
          data={data}
          renderItem={renderCarSelectionCards}
          style={style.listCarsContainer}
        />
      )}
    </View>
  )
}

export default DSCard

const style = StyleSheet.create({
  container: {
    flex: 1,
  },
  listCarsContainer: {
    paddingHorizontal: space.sm,
    paddingTop: space.xs,
    marginBottom: space.xl,
  },
  button: {
    marginHorizontal: space['4xs'],
    marginTop: space.xs,
  },
  gridCard: {
    marginHorizontal: space['2xs'],
    marginBottom: space.sm,
  },
  card: {
    marginBottom: space.sm,
  },
  selectionCards: {
    marginBottom: space.xl,
  },
  gridCardsWrap: {
    paddingHorizontal: space['3xs'] + space['2xs'],
    paddingTop: space.xs,
    marginBottom: space.xl,
  },
  separate: {
    flex: 1,
    height: 2,
    backgroundColor: trueGray[100],
    marginVertical: space.md,
    marginHorizontal: space.md,
  },
})
