import {
  BottomSheet,
  BottomSheetRef,
  <PERSON><PERSON>,
  Container,
  HStack,
  Input,
  Text,
} from '@liberty-ui-kit/components'
import React, {useRef, useState} from 'react'
import {StyleSheet, View, Image, Pressable} from 'react-native'
import {defaultColor, iconSize, screenSize, space} from '@liberty-ui-kit/theme'
import Carousel from 'react-native-reanimated-carousel'
import {useSharedValue} from 'react-native-reanimated'

const DSBottomSheet = () => {
  const [value, setValue] = useState('')
  const sheetRef = useRef<BottomSheetRef>(null)
  const dynamicSheet = useRef<BottomSheetRef>(null)
  const inputSheetRef = useRef<BottomSheetRef>(null)
  const carouselSheetRef = useRef<BottomSheetRef>(null)
  const progressValue = useSharedValue<number>(0)
  const baseOptions = {
    vertical: false,
    width: screenSize.width,
    height: screenSize.width * 0.5,
  } as const

  const renderItem = (item: string) => {
    return (
      <Pressable key={item}>
        <Image source={{uri: item}} style={style.item} />
      </Pressable>
    )
  }

  const onPress = () => {
    sheetRef.current?.present()
  }

  const onPressDynamic = () => {
    dynamicSheet.current?.present()
  }

  const onPressInput = () => {
    inputSheetRef.current?.present()
  }

  const onPressCarousel = () => {
    carouselSheetRef.current?.present()
  }

  const renderLanguage = ({title, icon}: {title: string; icon: string}) => (
    <HStack style={style.language} key={icon}>
      <Image style={style.flag} source={{uri: icon}} />
      <Text style={style.languageTitle}>{title}</Text>
    </HStack>
  )

  const renderCarousel = ({item}: {item: string}) => (
    <Image source={{uri: item}} key={item} style={style.image} />
  )

  return (
    <Container style={style.container}>
      <Button onPress={onPress}>Open Sticker</Button>
      <Button onPress={onPressDynamic}>Choose Language</Button>
      <Button onPress={onPressInput}>Input</Button>
      <Button onPress={onPressCarousel}>Carousel</Button>

      <BottomSheet enableDynamicSizing ref={carouselSheetRef} title="Carousel">
        <View style={{paddingBottom: space.md}}>
          <Carousel
            {...baseOptions}
            style={{
              width: screenSize.width,
            }}
            loop
            pagingEnabled={true}
            snapEnabled={true}
            autoPlayInterval={1500}
            onProgressChange={(_, absoluteProgress) =>
              (progressValue.value = absoluteProgress)
            }
            mode="parallax"
            modeConfig={{
              parallaxScrollingScale: 0.85,
              parallaxScrollingOffset: 70,
            }}
            data={carousel}
            renderItem={renderCarousel}
          />
        </View>
      </BottomSheet>

      <BottomSheet
        enableDynamicSizing
        ref={inputSheetRef}
        title="Why won't my car start?"
      >
        <View style={style.withInput}>
          <Text style={style.des}>
            If the car makes a rapid clicking sound when you turn the key but
            won’t start. Its usually caused by a dying or dead battery, loose...
          </Text>
          <Input
            icon="instagram"
            inBottomSheet
            value={value}
            onChangeText={setValue}
          />
        </View>
      </BottomSheet>

      <BottomSheet
        enableDynamicSizing
        ref={dynamicSheet}
        title="Choose Language"
        subTitle="4+ languages"
      >
        <View style={[style.content, style.noWrap]}>
          {language.map(renderLanguage)}
        </View>
      </BottomSheet>
      <BottomSheet ref={sheetRef} title="Stickers">
        <HStack style={style.content}>{stickers.map(renderItem)}</HStack>
      </BottomSheet>
    </Container>
  )
}

export default DSBottomSheet

const style = StyleSheet.create({
  container: {
    padding: space.md,
    gap: space.md,
  },
  content: {
    flexWrap: 'wrap',
    padding: space.sm,
    gap: space.md,
  },
  item: {
    aspectRatio: 1 / 1,
    width: (screenSize.width - space.md * 5) / 4,
  },
  languageTitle: {
    fontWeight: '500',
  },
  flag: {
    width: iconSize.lg,
    height: iconSize.lg,
  },
  language: {
    //
    gap: space.sm,
  },
  des: {
    color: defaultColor[700],
  },
  image: {
    width: screenSize.width,
    height: screenSize.width * 0.5,
    borderRadius: space.sm,
  },
  noWrap: {
    //
    flexWrap: 'wrap',
  },
  withInput: {
    padding: space.md,
    gap: space.md,
  },
})

const language = [
  {
    title: 'Việt Nam',
    icon: 'https://cdn-icons-png.flaticon.com/128/323/323319.png',
  },
  {
    title: 'Chinese',
    icon: 'https://cdn-icons-png.flaticon.com/128/197/197375.png',
  },
  {
    title: 'Korean',
    icon: 'https://cdn-icons-png.flaticon.com/128/197/197582.png',
  },
  {
    title: 'Belgium',
    icon: 'https://cdn-icons-png.flaticon.com/128/197/197583.png',
  },
  {
    title: 'Brazil',
    icon: 'https://cdn-icons-png.flaticon.com/128/197/197386.png',
  },
  {
    title: 'UK',
    icon: 'https://cdn-icons-png.flaticon.com/128/197/197374.png',
  },
  {
    title: 'Canada',
    icon: 'https://cdn-icons-png.flaticon.com/128/197/197430.png',
  },
]

const stickers = [
  'https://tlgrm.eu/_/stickers/351/df7/351df72b-aa79-4692-bd9d-4eb5a72d202a/192/5.webp',
  'https://tlgrm.eu/_/stickers/351/df7/351df72b-aa79-4692-bd9d-4eb5a72d202a/192/3.webp',
  'https://tlgrm.eu/_/stickers/351/df7/351df72b-aa79-4692-bd9d-4eb5a72d202a/192/26.webp',
  'https://tlgrm.eu/_/stickers/351/df7/351df72b-aa79-4692-bd9d-4eb5a72d202a/192/24.webp',
  'https://tlgrm.eu/_/stickers/351/df7/351df72b-aa79-4692-bd9d-4eb5a72d202a/192/20.webp',
  'https://tlgrm.eu/_/stickers/351/df7/351df72b-aa79-4692-bd9d-4eb5a72d202a/192/8.webp',
  'https://tlgrm.eu/_/stickers/351/df7/351df72b-aa79-4692-bd9d-4eb5a72d202a/192/13.webp',
]

const carousel = [
  'https://img.freepik.com/free-psd/gradient-world-health-day-template_23-**********.jpg?size=626&ext=jpg',
  'https://img.freepik.com/free-psd/liquid-banner-template_23-**********.jpg?size=626&ext=jpg',
  'https://img.freepik.com/free-vector/modern-gradient-background-blurred_125540-1213.jpg?size=626&ext=jpg&ga=GA1.1.**********.**********&semt=ais',
  'https://img.freepik.com/free-psd/world-health-day-banner-template_23-**********.jpg?size=626&ext=jpg&ga=GA1.1.**********.**********&semt=ais',
  'https://img.freepik.com/free-psd/liquid-banner-template_23-**********.jpg?size=626&ext=jpg&ga=GA1.1.**********.**********&semt=ais',
  'https://img.freepik.com/free-vector/gradient-abstract-fluid-technology-sale-background_23-**********.jpg?size=626&ext=jpg&ga=GA1.1.**********.**********&semt=ais',
]
