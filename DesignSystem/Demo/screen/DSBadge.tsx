import {
  Badge,
  BadgeType,
  Container,
  Divider,
  HStack,
  Text,
} from '@liberty-ui-kit/components'
import React from 'react'
import {StyleSheet, View} from 'react-native'
import {fontSize, space} from '@liberty-ui-kit/theme'
import {Status} from '@liberty-ui-kit/types'

const badgeStatus: Status[] = [
  'primary',
  'default',
  'success',
  'warning',
  'danger',
  'info',
]

const badgeType: BadgeType[] = ['solid', 'outline', 'subtle']

const DSBadge = () => {
  return (
    <Container style={style.container}>
      {badgeType.map(type => (
        <View style={style.content} key={type}>
          <Text style={style.title}>{type}</Text>
          <HStack style={style.row} gap={space.sm}>
            {badgeStatus.map((item: Status) => (
              <Badge status={item} key={item} type={type}>
                {item}
              </Badge>
            ))}
          </HStack>
          <Divider />
        </View>
      ))}

      <View style={style.content}>
        <Text style={style.title}>Small Size</Text>
        <HStack style={style.row} gap={space.sm}>
          {badgeStatus.map((item: Status) => (
            <Badge status={item} key={item} size="sm">
              {item}
            </Badge>
          ))}
        </HStack>
        <Divider />
      </View>
    </Container>
  )
}

export default DSBadge

const style = StyleSheet.create({
  container: {
    padding: space.md,
    rowGap: space.md,
  },
  content: {
    rowGap: space.md,
  },
  row: {
    flexWrap: 'wrap',
  },
  title: {
    fontSize: fontSize.lg,
    textTransform: 'uppercase',
    fontWeight: '600',
  },
})
