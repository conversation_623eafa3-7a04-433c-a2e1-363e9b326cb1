import {
  Container,
  Di<PERSON>r,
  But<PERSON>,
  <PERSON><PERSON><PERSON>ck,
  IconName,
  Icon,
} from '@liberty-ui-kit/components'
import React from 'react'
import {<PERSON>ert, FlatList, StyleSheet, View} from 'react-native'
import {space} from '@liberty-ui-kit/theme'

const buttonType = ['solid', 'outline', 'ghost']
const icon = ['bank', 'add', 'check']

const DSButton = () => {
  const onPress = () => {
    Alert.alert('Pressed')
  }

  const renderItem = ({item, index}: any) => (
    <View style={style.row}>
      <Button onPress={onPress} style={style.button} type={item}>
        {item}
      </Button>
      <Button disabled style={style.button} type={item}>
        Disabled
      </Button>
      <HStack gap={space.sm}>
        <Button style={style.button} loading type={item}>
          Loading
        </Button>
        <Button
          onPress={onPress}
          style={style.button}
          RightItem={({color}) => <Icon color={color} icon="bank" size={14} />}
          type={item}
        >
          With Item
        </Button>
        <Button
          onPress={onPress}
          style={style.button}
          RightItem={({color}) => (
            <Icon color={color} icon={icon[index] as IconName} size={14} />
          )}
          type={item}
        />
      </HStack>
    </View>
  )

  const ListFooterComponent = () => {
    return <View style={style.footerList}></View>
  }

  return (
    <Container>
      <FlatList
        ItemSeparatorComponent={() => <Divider style={style.divider} />}
        data={buttonType}
        renderItem={renderItem}
        style={style.list}
        ListFooterComponent={ListFooterComponent}
      />
    </Container>
  )
}

export default DSButton

const style = StyleSheet.create({
  button: {
    // flex: 1,
  },
  list: {
    paddingVertical: space.md,
  },
  divider: {
    marginVertical: space.md,
  },
  row: {
    marginHorizontal: space.md,
    rowGap: space.sm,
  },
  footerList: {
    columnGap: space.md,
    padding: space.md,
  },
})
