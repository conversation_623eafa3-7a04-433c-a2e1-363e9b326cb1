import {
  BottomV<PERSON><PERSON>,
  But<PERSON>,
  Container,
  HStack,
  Input,
  Radio,
  Text,
} from '@liberty-ui-kit/components'
import React from 'react'
import {<PERSON><PERSON>, ScrollView, StyleSheet, View} from 'react-native'
import {fontSize, space} from '@liberty-ui-kit/theme'

const DSBottomView = () => {
  const onPress = () => {
    Alert.alert('Pressed')
  }

  return (
    <Container>
      <ScrollView style={style.container}>
        <View style={style.header}>
          <Text style={style.title}>Login</Text>
          <Input icon="phone" placeholder="Phone Number" />
          <Input icon="locked" placeholder="Password" secureTextEntry />
        </View>
      </ScrollView>
      <BottomView avoidKeyboard>
        <Button onPress={onPress}>Bottom View</Button>
      </BottomView>
    </Container>
  )
}

export default DSBottomView

const style = StyleSheet.create({
  container: {
    padding: space.md,
    rowGap: space.md,
  },
  title: {
    fontWeight: '700',
    fontSize: fontSize['3xl'],
  },
  header: {
    rowGap: space.md,
  },
})
