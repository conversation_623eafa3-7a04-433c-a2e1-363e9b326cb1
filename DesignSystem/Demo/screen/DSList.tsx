import {CheckBox, Container, List} from '@liberty-ui-kit/components'
import React from 'react'
import {StyleSheet, FlatList} from 'react-native'
import {colors, space} from '@liberty-ui-kit/theme'
import {useToggle} from '@liberty-ui-kit/hook'
import {GridCarCard} from '@liberty-ui-kit/components/GridCarCard'
import {CAR_TYPE} from '@stores/slices/home/<USER>'

const EXAMPLE_CAR = {
  name: 'Chervolet colorado dioe xs 2022 colorado Chervolet colorado dioe xs 2022 colorado',
  type: CAR_TYPE.RENT,
  price: '2000',
  description: 'Lorem ipsum dolor sit Lorem ipsum dolor sit',
}

const DSList = () => {
  const [isFlatlist, toggleFlatlist] = useToggle()

  const renderItem = ({item, index}: {item: number; index: number}) => {
    return (
      <GridCarCard
        key={`car-grid-${index}`}
        address="Phnom penh"
        car={EXAMPLE_CAR}
        index={index}
      />
    )
  }

  return (
    <Container level={2}>
      <CheckBox
        onCheck={toggleFlatlist}
        style={style.checkBox}
        checked={isFlatlist}
      >
        Sử dụng Flatlist
      </CheckBox>
      {isFlatlist ? (
        <FlatList
          data={data}
          numColumns={2}
          renderItem={renderItem}
          contentContainerStyle={style.list}
          // ItemSeparatorComponent={<View style={style.row} />}
        />
      ) : (
        <List
          data={data}
          numColumns={2}
          renderItem={renderItem}
          contentContainerStyle={style.list}
          estimatedItemSize={270}
          // ItemSeparatorComponent={<View style={style.row} />}
        />
      )}
    </Container>
  )
}

export default DSList

const style = StyleSheet.create({
  list: {
    paddingHorizontal: space.md,
    paddingBottom: space.md,
  },
  checkBox: {
    padding: space.md,
    backgroundColor: colors.white,
  },
})

const data = Array(1000).fill(0)
