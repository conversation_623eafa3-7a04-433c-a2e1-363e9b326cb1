import {Badge, Container, Slider, Text} from '@liberty-ui-kit/components'
import React from 'react'
import {StyleSheet, Text as RNText} from 'react-native'
import {fontSize, iconSize, space} from '@liberty-ui-kit/theme'
import {useTheme} from '@liberty-ui-kit/hook'

const DSSlider = () => {
  return (
    <Container style={style.container}>
      <Slider
        value={[0, 20]}
        minimumValue={0}
        maximumValue={100}
        step={10}
        renderBubble={({value}) => (
          <Text style={{color: 'white'}}>${value}</Text>
        )}
      />

      <Slider
        style={{marginTop: space['4xl']}}
        value={[0, 1000]}
        minimumValue={0}
        maximumValue={1000}
        step={1}
      />
    </Container>
  )
}

export default DSSlider

const style = StyleSheet.create({
  container: {
    padding: space.md,
  },
})
