import {
  Container,
  NavigationBar,
  StatusBar,
  Text,
  Icon,
} from '@liberty-ui-kit/components'
import React from 'react'
import {ScrollView, StyleSheet, View} from 'react-native'
import {iconSize, space} from '@liberty-ui-kit/theme'
import {useTheme} from '@liberty-ui-kit/hook'

const DSNavigationBar = () => {
  const {theme} = useTheme()
  return (
    <Container level={2}>
      <StatusBar
        barStyle={theme === 'light' ? 'dark-content' : 'light-content'}
      />
      <NavigationBar title="Navigation Bar" />

      <ScrollView>
        <NavigationBar
          title="Liberty Carz"
          RightItem={() => <Text>Filter</Text>}
        />
        <NavigationBar
          title="Navigation Bar"
          RightItem={({color}) => (
            <Icon icon="copy" size={iconSize.md} color={color} />
          )}
        />
        <NavigationBar title="Rental Car" subTitle="10 items" />
      </ScrollView>
    </Container>
  )
}

export default DSNavigationBar

const style = StyleSheet.create({})
