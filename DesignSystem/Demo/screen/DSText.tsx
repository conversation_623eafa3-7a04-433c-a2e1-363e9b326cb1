import {Button, Container, HStack, Text} from '@liberty-ui-kit/components'
import React, {useState} from 'react'
import {StyleSheet, TextStyle, View} from 'react-native'
import {fontSize, space} from '@liberty-ui-kit/theme'

const locale = {
  en: 'Sorry, you can upload only one video',
  zh: '柬埔寨金边市 水净化区 ，Keo Chenda街，Kings View办公楼B座1楼， 。',
  km: 'បូករួមនៃលទ្ធផលដែលកើតមានឡើង',
}

const weight: TextStyle['fontWeight'][] = [
  '100',
  '200',
  '300',
  '400',
  '500',
  '600',
  '700',
  '800',
]
type LocaleType = 'en' | 'zh' | 'km'

const DSText = () => {
  const [text, changeText] = useState<LocaleType>('en')
  const [fontWeight, setFontWeight] = useState<TextStyle['fontWeight']>('400')

  const changeLocale = (locale: LocaleType) => {
    changeText(locale)
  }

  return (
    <Container style={style.container}>
      <Text style={[style.text, style.en]}>Finished with error</Text>
      <Text style={[style.text, style.km]}>
        សូមស្វាគមន៍មកកាន់កម្មវិធីដៃគូរសហការ Libery Carz
      </Text>
      <Text style={[style.text, style.zh]}>
        为以及煽动这种歧视的任何行为之害
      </Text>
      <View style={style.bottom}>
        <Text
          style={[
            style.text,
            {
              fontWeight,
              fontFamily:
                text === 'zh'
                  ? 'Noto Sans SC'
                  : text === 'km'
                  ? 'Kantumruy Pro'
                  : 'Inter',
            },
          ]}
        >
          {locale?.[text] ?? ''}
        </Text>
        <HStack gap={space.md}>
          <Button
            style={style.button}
            type={text === 'en' ? 'solid' : 'outline'}
            onPress={() => changeLocale('en')}
          >
            English
          </Button>
          <Button
            onPress={() => changeLocale('km')}
            style={style.button}
            type={text === 'km' ? 'solid' : 'outline'}
          >
            Khmer
          </Button>
          <Button
            onPress={() => changeLocale('zh')}
            style={style.button}
            type={text === 'zh' ? 'solid' : 'outline'}
          >
            Chinese
          </Button>
        </HStack>
        <HStack style={style.weightButton}>
          {weight.map((item: TextStyle['fontWeight']) => {
            return (
              <Button
                type={fontWeight === item ? 'solid' : 'outline'}
                key={item}
                onPress={() => setFontWeight(item)}
              >
                {item}
              </Button>
            )
          })}
        </HStack>
      </View>
    </Container>
  )
}

export default DSText

const style = StyleSheet.create({
  container: {
    padding: space.md,
    rowGap: space.md,
  },
  text: {
    fontSize: fontSize['4xl'],
  },
  en: {
    fontFamily: 'Inter',
    fontWeight: '900',
  },
  km: {
    fontFamily: 'Kantumruy Pro',
    fontWeight: '900',
  },
  zh: {
    fontFamily: 'Noto Sans SC',
    fontWeight: '900',
  },
  button: {
    flex: 1,
  },
  bottom: {
    marginTop: space['4xl'],
    gap: space.md,
  },
  weightButton: {
    gap: space.xs,
    flexWrap: 'wrap',
  },
})
