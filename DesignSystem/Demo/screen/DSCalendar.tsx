import {Bad<PERSON>, Button, Container} from '@liberty-ui-kit/components'
import React, {useRef, useState} from 'react'
import {StyleSheet, Text as RNText, Text} from 'react-native'
import {fontSize, iconSize, space} from '@liberty-ui-kit/theme'
import {openCalendar, openModal} from '@liberty-ui-kit/common'

const DSCalendar = () => {
  const [date, setDate] = useState('')
  const open = () => {
    openCalendar({
      title: 'Calendar',
      onConfirm: day => setDate(day.dateString),
    })
  }

  return (
    <Container style={style.container}>
      <Text>{date}</Text>
      <Button onPress={open}>Open</Button>
    </Container>
  )
}

export default DSCalendar

const style = StyleSheet.create({
  container: {
    padding: space.md,
    rowGap: space.md,
  },
})
