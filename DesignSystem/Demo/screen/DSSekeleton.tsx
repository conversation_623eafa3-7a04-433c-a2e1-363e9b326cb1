import React, {useState} from 'react'
import {FlatList, StyleSheet, View} from 'react-native'
import {space, trueGray} from '@liberty-ui-kit/theme'
import {ServiceSelectionSkeleton} from '@liberty-ui-kit/components/Skeleton'
import {Button} from '@liberty-ui-kit/components'
import {CarSkeleton} from '@liberty-ui-kit/components/CarCard/CarSkeleton'
import {GridCarSkeleton} from '@liberty-ui-kit/components/GridCarCard/GridCarSkeleton'
import {BookingCarSkeleton} from '@liberty-ui-kit/components/BookingCarCard/BookingCarSkeleton'
import {CarSelectionSkeleton} from '@liberty-ui-kit/components/CarSelectionCard/CarSelectionSkeleton'

const DSSkeleton = () => {
  const [type, setType] = useState(1)
  const data = [1, 2, 3, 4, 5, 6, 7, 8]

  const renderCarSelectionSkeleton = ({
    item,
    index,
  }: {
    item: string | number
    index: number
  }) => {
    return (
      <CarSelectionSkeleton
        key={`car-selection-skeleton-${index}`}
        style={{marginBottom: space['3xl']}}
      />
    )
  }

  const renderBookingCarSkeleton = ({
    item,
    index,
  }: {
    item: number
    index: number
  }) => {
    return <BookingCarSkeleton key={`booking-car-skeleton-${index}`} />
  }

  const renderServiceSelectionSkeleton = ({
    item,
    index,
  }: {
    item: number
    index: number
  }) => {
    return <ServiceSelectionSkeleton key={`booking-skeleton-${index}`} />
  }

  const renderGridCardSkeleton = ({
    item,
    index,
  }: {
    item: number
    index: number
  }) => {
    return (
      <GridCarSkeleton
        key={`grid-car-skeleton-${index}`}
        style={style.gridCard}
      />
    )
  }

  const renderCarSkeleton = ({item, index}: {item: number; index: number}) => {
    return (
      <CarSkeleton key={`car-skeleton-${index}`} style={style.skeletonCard} />
    )
  }

  const chooseType = (type: number) => () => {
    setType(type)
  }

  const checkType = (buttonType: number) => {
    return buttonType === type ? 'solid' : 'outline'
  }

  const renderSeparate = ({item, index}: {item: number; index: number}) => {
    return <View style={style.separate} />
  }

  return (
    <View style={style.container}>
      <View>
        <Button
          style={style.button}
          type={checkType(1)}
          onPress={chooseType(1)}
        >
          {'Car Selection'}
        </Button>
        <Button
          style={style.button}
          type={checkType(2)}
          onPress={chooseType(2)}
        >
          {'Booking Car'}
        </Button>
        <Button
          style={style.button}
          type={checkType(3)}
          onPress={chooseType(3)}
        >
          {'Service Selection'}
        </Button>
        <Button
          style={style.button}
          type={checkType(4)}
          onPress={chooseType(4)}
        >
          {'Grid Card'}
        </Button>
        <Button
          style={style.button}
          type={checkType(5)}
          onPress={chooseType(5)}
        >
          {'Car'}
        </Button>
      </View>
      {type === 1 && (
        <FlatList
          data={data}
          renderItem={renderCarSelectionSkeleton}
          style={style.flatList}
        />
      )}
      {type === 2 && (
        <FlatList
          data={data}
          renderItem={renderBookingCarSkeleton}
          ItemSeparatorComponent={renderSeparate}
        />
      )}
      {type === 3 && (
        <FlatList
          data={data}
          renderItem={renderServiceSelectionSkeleton}
          style={style.flatList}
        />
      )}
      {type === 4 && (
        <FlatList
          numColumns={2}
          data={data}
          renderItem={renderGridCardSkeleton}
          style={style.gridSkeletonWrap}
        />
      )}
      {type === 5 && (
        <FlatList
          data={data}
          renderItem={renderCarSkeleton}
          style={style.flatList}
        />
      )}
    </View>
  )
}

export default DSSkeleton

const style = StyleSheet.create({
  container: {
    flex: 1,
  },
  button: {
    marginTop: space.xs,
  },
  gridCard: {
    marginHorizontal: space['2xs'],
  },
  flatList: {
    padding: space.md,
  },
  skeletonCard: {
    marginBottom: space.md,
  },
  gridSkeletonWrap: {
    paddingVertical: space.md,
    paddingHorizontal: space.xs,
  },
  separate: {
    flex: 1,
    height: 2,
    backgroundColor: trueGray[100],
    marginVertical: space.md,
    marginHorizontal: space.md,
  },
})
