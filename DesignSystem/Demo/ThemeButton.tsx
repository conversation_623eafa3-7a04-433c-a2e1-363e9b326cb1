import {View, StyleSheet, Pressable} from 'react-native'
import React from 'react'
import {useSafeAreaInsets} from 'react-native-safe-area-context'
import {colors, screenSize, space} from '@liberty-ui-kit/theme'
import {toggleTheme} from '@app/stores/slices/theme/slice'
import store from '@stores'
import {Icon} from '@liberty-ui-kit/components'

const ThemeButton = () => {
  const insets = useSafeAreaInsets()
  const bottom = insets.bottom + space.md

  const onChange = () => {
    store.dispatch(toggleTheme())
  }

  return (
    <View style={[style.container, {bottom}]}>
      <Pressable onPress={onChange} style={style.button}>
        <Icon size={24} icon="actionable-insights" color="white" />
      </Pressable>
    </View>
  )
}

export default ThemeButton

const style = StyleSheet.create({
  container: {
    position: 'absolute',
    right: space.md,
  },
  button: {
    //
    backgroundColor: colors.primary,
    padding: space.sm,
    borderRadius: screenSize.width,
  },
})
