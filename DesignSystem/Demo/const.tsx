export enum DSScreen {
  Button = 'Button',
  Text = 'Text',
  List = 'List',
  NavigationBar = 'NavigationBar',
  BottomSheet = 'BottomSheet',
  Image = 'Image',
  ReadMore = 'ReadMore',
  Notify = 'Notify',
  Input = 'Input',
  Card = 'Card',
  AlertPopup = 'AlertPopup',
  Calendar = 'Calendar',
  'Information Card' = 'Information Card',
  Slider = 'Slider',
  Group = 'Group',
  StepIndicator = 'StepIndicator',
  Avatar = 'Avatar',
  CheckBox = 'CheckBox',
  Radio = 'Radio',
  Badge = 'Badge',
  BottomView = 'BottomView',
  Skeleton = 'Skeleton',
  Switch = 'Switch',
}
