import React, {ReactNode, memo, useRef} from 'react'
import {View, StyleSheet, ViewProps, Pressable} from 'react-native'
import {
  colors,
  defaultColor,
  dark,
  primary,
  fontSize,
  iconSize,
  space,
  danger,
  trueGray,
  warning,
} from '@liberty-ui-kit/theme'
import {
  adapterImageUrlOutput,
  convertRentalCarPrice,
  formatCurrency,
} from '@utils/utils'
import {
  Badge,
  CheckBox,
  HStack,
  Icon,
  Image,
  Text,
} from '@liberty-ui-kit/components'
import {
  BOOKING_STATUS_BADGE_COLOR,
  CAR_TYPE,
  CarType,
} from '@stores/slices/home/<USER>'
import {translate} from '@i18n/translate'
import {TxKeyPath} from '@i18n/i18n'
import CarLikeButton from '../CarLikeButton'
import {Status} from '@liberty-ui-kit/types'
import {useDebouncePress} from '@hooks/useDebouncePress'
import {CarRowInfo} from '../CarRowInfo'
import {Images} from '@constant/themes/images'
import {CarStatus} from '@constant/constant/car.enum'
import {getCarStoreNameByLanguage, getLoanPricePerMonth} from '@utils/carUtils'
import {ThumbnailType} from '@app-types/ThumbnailType'
import {blue} from '@constant/themes/colors'

export interface CarCardProps extends ViewProps {
  car: CarType
  address?: string
  onClickCarCard?: () => void
  isShowFavorite?: boolean
  isSelected?: boolean
  isCanceled?: boolean
  Footer?: ReactNode
  Header?: ReactNode
  status?: string
  isBooking?: boolean
  callbackLikeValue?: (isLiked: boolean) => void
  type?: 'base' | 'my-car'
}

// Section 1: Car Image Component
const CarImageSection = ({car}: {car: CarType; status?: string}) => {
  const carImage = car?.cover ?? car?.extraData?.cover
  return (
    <View style={style.wrapCoverImage}>
      <Image
        style={style.img}
        source={adapterImageUrlOutput(carImage, ThumbnailType.REACTANGLE_XL)}
        resizeMode="cover"
      />
      {[CarStatus.SOLD, CarStatus.SOLD_BY_MERCHANT].includes(
        car?.status as CarStatus
      ) && (
        <>
          <View style={[style.img, style.overlayContainer]} />
          <Image
            style={style.soldImage}
            source={Images.soldImage}
            resizeMode="cover"
          />
        </>
      )}
    </View>
  )
}

// Section 2: Booking Status Component
const BookingStatusSection = ({status}: {status?: string}) => {
  if (!status) return null
  return (
    <View style={style.bookingStatusWrap}>
      <Badge
        status={BOOKING_STATUS_BADGE_COLOR[status] as Status}
        size={'sm'}
        style={style.bookingStatus}
      >
        {translate(`common.${status}` as TxKeyPath)}
      </Badge>
    </View>
  )
}

// Section 3: Car Status Icons Component
const CarStatusIconsSection = ({
  car,
  hasNew,
  hasDiscount,
  hasChecked,
  hasUsed,
  hasLoan,
  isShowFavorite,
  infoRowRef,
  callbackLikeValue,
}: {
  car: CarType
  hasNew: boolean
  hasDiscount: boolean
  hasChecked: boolean
  hasUsed: boolean
  hasLoan: boolean
  isShowFavorite: boolean
  infoRowRef: React.RefObject<any>
  callbackLikeValue?: (isLiked: boolean) => void
}) => {
  return (
    <View style={style.statusWrap}>
      <HStack style={style.badgeField} gap={space['3xs']}>
        {hasNew && (
          <Icon icon="new-car-sb" size={iconSize.xs} color={danger[500]} />
        )}
        {hasDiscount && (
          <Icon icon="best-offer-sb" size={iconSize.xs} color={danger[500]} />
        )}
        {hasChecked && (
          <Icon icon="car-check-sb" size={iconSize.xs} color={blue[500]} />
        )}
        {hasUsed && (
          <Icon icon="used-car-sb" size={iconSize.xs} color={trueGray[500]} />
        )}
        {hasLoan && (
          <Icon icon="car-loan-sb" size={iconSize.xs} color={warning[500]} />
        )}
      </HStack>
      {isShowFavorite && car?.id && (
        <CarLikeButton
          infoRowRef={infoRowRef}
          car={car}
          style={style.likeButton}
          size={iconSize.xs}
          callbackLikeValue={callbackLikeValue}
        />
      )}
    </View>
  )
}

// Section 4: Car Name and Price Component
const CarNamePriceSection = ({
  car,
  isRent,
  displayRentalPrice,
  displayUnitPrice,
  hasPrice,
  hasDiscount,
  hasLoan,
}: {
  car: CarType
  isRent: boolean
  displayRentalPrice: string
  displayUnitPrice: string
  hasPrice: boolean
  hasDiscount: boolean
  hasLoan: boolean
}) => {
  return (
    <View>
      <HStack gap={space['3xs']} style={style.priceWrap}>
        <Text style={[style.price, !hasPrice && style.hidePrice]}>
          {isRent
            ? displayRentalPrice
            : formatCurrency(car?.price - (car?.discountValue || 0))}
          {isRent && (
            <Text style={[style.period, style.unit]}>
              /{translate(`carDetail.${displayUnitPrice}` as TxKeyPath)}
            </Text>
          )}
        </Text>
        {hasDiscount && (
          <Text style={style.discountPrice}>{formatCurrency(car?.price)}</Text>
        )}
      </HStack>
      {hasLoan ? (
        <HStack style={style.loanCalculate}>
          <Text style={style.loanCalculateText} numberOfLines={1}>
            {translate('carLoan.from')}{' '}
            {formatCurrency(
              getLoanPricePerMonth(
                car?.price - (car?.discountValue || 0),
                car?.loanInformation?.bank?.interestRate || 0,
                car?.loanInformation?.bank?.downPayment || 0,
                car?.loanInformation?.bank?.period || 60
              )
            )}
            {translate('carLoan.perMonth')}
          </Text>
        </HStack>
      ) : (
        <View style={style.emptyLoanCalculate} />
      )}
    </View>
  )
}

// Section 5: Car Address Component
const CarAddressSection = ({
  address,
  carId,
}: {
  address?: string
  carId?: number
}) => {
  return (
    <View>
      {!address && <View style={style.emptyAddress} />}
      {carId && <CarRowInfo carId={carId} />}
      {address && (
        <HStack>
          <Icon size={12} icon="location" color={dark[500]} />
          <Text style={style.grayText}>{address}</Text>
        </HStack>
      )}
    </View>
  )
}

// Main CarCard Component
export const CarCard = memo((props: CarCardProps) => {
  const {
    car,
    address,
    onClickCarCard,
    isShowFavorite = true,
    isSelected = undefined,
    Footer,
    status,
    isCanceled,
    callbackLikeValue,
    isBooking = false,
    Header,
    type,
  } = props

  const {displayRentalPrice, displayUnitPrice} = convertRentalCarPrice(car)
  const infoRowRef = useRef<any>()
  const {debounce} = useDebouncePress()

  const isRent = car?.type === CAR_TYPE.RENT
  const hasDiscount = !!car?.discountValue && !!car?.price
  const hasNew = car?.state === 'new'
  const hasUsed = car?.state === 'used'
  const hasChecked = !!car?.isChecked
  const hasLoan = !!car?.loanInformation?.isSupportLoan
  const hasPrice = isRent ? !!displayRentalPrice : !!car?.price
  const carId = car?.id

  let addressClone = address
  if (car?.carStore && !addressClone) {
    addressClone = getCarStoreNameByLanguage(car.carStore)
  }

  const shouldShowCarStatus =
    (hasDiscount || hasNew || hasUsed || hasChecked || hasLoan) &&
    !isBooking &&
    !isRent &&
    type !== 'my-car'

  const onClick = () => {
    if (isSelected !== undefined) {
      onClickCarCard?.()
    } else {
      debounce(onClickCarCard)
    }
  }

  return (
    <Pressable onPress={onClick}>
      <HStack
        {...props}
        style={[
          style.container,
          props?.style,
          isCanceled && style.canceledCard,
        ]}
      >
        <CarImageSection car={car} status={status} />
        <BookingStatusSection status={status} />
        <View style={style.infoWrap}>
          <View style={style.subInfoWrap}>
            <View>
              {shouldShowCarStatus && (
                <CarStatusIconsSection
                  car={car}
                  hasNew={hasNew}
                  hasDiscount={hasDiscount}
                  hasChecked={hasChecked}
                  hasUsed={hasUsed}
                  hasLoan={hasLoan}
                  isShowFavorite={isShowFavorite}
                  infoRowRef={infoRowRef}
                  callbackLikeValue={callbackLikeValue}
                />
              )}
              {Header || (
                <View style={style.row}>
                  <Text style={style.name} numberOfLines={1}>
                    {car?.name ?? car?.extraData?.name}
                  </Text>
                  {!shouldShowCarStatus && isShowFavorite && carId && (
                    <CarLikeButton
                      infoRowRef={infoRowRef}
                      car={car}
                      style={[style.likeButton]}
                      size={iconSize.xs}
                      callbackLikeValue={callbackLikeValue}
                    />
                  )}
                </View>
              )}
              <CarNamePriceSection
                car={car}
                isRent={isRent}
                displayRentalPrice={displayRentalPrice?.toString()}
                displayUnitPrice={displayUnitPrice}
                hasPrice={hasPrice}
                hasDiscount={hasDiscount}
                hasLoan={hasLoan}
              />
            </View>
            {Footer || (
              <CarAddressSection address={addressClone} carId={carId} />
            )}
          </View>

          {isSelected !== undefined && (
            <CheckBox
              onCheck={onClickCarCard}
              checked={isSelected}
              style={style.checkbox}
            />
          )}
        </View>
      </HStack>
    </Pressable>
  )
})

const style = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 4,
    overflow: 'hidden',
    alignItems: 'flex-start',
  },
  canceledCard: {
    opacity: 0.5,
  },
  img: {
    height: '100%',
    width: 148,
  },
  infoWrap: {
    flex: 1,
    padding: space.xs,
  },
  subInfoWrap: {
    flex: 1,
    justifyContent: 'space-between',
  },
  statusWrap: {
    flexDirection: 'row',
    marginBottom: space['3xs'],
    justifyContent: 'space-between',
  },
  badgeField: {
    flexDirection: 'row',
    backgroundColor: defaultColor[100],
    borderRadius: space['4xs'],
    paddingHorizontal: space['4xs'],
    paddingVertical: space['4xs'],
  },
  name: {
    flex: 1,
    color: defaultColor[700],
    fontSize: fontSize.sm,
    fontWeight: '500',
  },
  priceWrap: {
    flexDirection: 'row',
  },
  price: {
    color: primary[800],
    fontWeight: '700',
  },
  hidePrice: {
    opacity: 0,
  },
  period: {
    color: defaultColor[400],
    fontWeight: '500',
  },
  discountPrice: {
    textDecorationLine: 'line-through',
    fontSize: fontSize.xs,
    fontWeight: '400',
    color: defaultColor[400],
    alignSelf: 'flex-end',
    marginBottom: space['4xs'],
  },
  unit: {
    color: defaultColor[400],
  },
  grayText: {
    color: dark[500],
    fontSize: fontSize.xs,
    lineHeight: 0,
    marginRight: 10,
  },
  likeButton: {
    width: iconSize.xs,
    height: iconSize.xs,
    alignItems: 'flex-end',
    alignSelf: 'flex-start',
    marginLeft: space['3xs'],
  },
  row: {
    flexDirection: 'row',
  },
  bookingStatusWrap: {
    position: 'absolute',
  },
  bookingStatus: {
    borderTopLeftRadius: 2,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 2,
    borderBottomLeftRadius: 0,
  },
  checkbox: {
    alignSelf: 'flex-start',
  },
  overlayContainer: {
    backgroundColor: colors.black,
    opacity: 0.7,
    position: 'absolute',
  },
  soldImage: {
    width: 96,
    height: 57,
    position: 'absolute',
  },
  wrapCoverImage: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loanCalculate: {
    backgroundColor: warning[100],
    alignSelf: 'flex-start',
    paddingHorizontal: space['3xs'],
    paddingVertical: space['3xs'],
    borderRadius: space['3xs'],
    marginBottom: space['4xs'],
  },
  loanCalculateText: {
    color: warning[500],
    fontSize: fontSize.xs,
  },
  emptyLoanCalculate: {
    height: space.xl,
  },
  emptyAddress: {
    height: space.md - 1,
  },
})
