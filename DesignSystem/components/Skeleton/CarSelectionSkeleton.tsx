import {View, StyleSheet, ViewProps} from 'react-native'
import React from 'react'
import {Placeholder, PlaceholderLine, Fade} from 'rn-placeholder'
import {colors, space} from '@liberty-ui-kit/theme'

export interface CarSelectionSkeletonProps extends ViewProps {}

const SKELETON_COLOR = colors.background // FIXME: Change this color

export const CarSelectionSkeleton = (props: CarSelectionSkeletonProps) => {
  return (
    <View style={style.container} {...props}>
      <Placeholder Animation={Fade}>
        <View style={style.row}>
          <PlaceholderLine
            noMargin
            style={style.image}
            color={SKELETON_COLOR}
          />
          <View style={style.flex}>
            <View style={style.statusRow}>
              <View style={style.row}>
                <PlaceholderLine
                  noMargin
                  style={[style.statusTag, style.statusFirst]}
                  color={SKELETON_COLOR}
                />
                <PlaceholderLine
                  noMargin
                  style={[style.statusTag, style.statusSecond]}
                  color={SKELETON_COLOR}
                />
                <PlaceholderLine
                  noMargin
                  style={[style.statusTag, style.statusThird]}
                  color={SKELETON_COLOR}
                />
              </View>
              <PlaceholderLine
                noMargin
                style={style.checkbox}
                color={SKELETON_COLOR}
              />
            </View>
            <PlaceholderLine
              noMargin
              style={style.title}
              color={SKELETON_COLOR}
            />
            <PlaceholderLine
              noMargin
              style={style.price}
              color={SKELETON_COLOR}
            />
            <View style={style.likeViewWrap}>
              <PlaceholderLine
                noMargin
                style={style.likeView}
                color={SKELETON_COLOR}
              />
              <PlaceholderLine
                noMargin
                style={style.likeView}
                color={SKELETON_COLOR}
              />
            </View>
          </View>
        </View>
      </Placeholder>
    </View>
  )
}

const style = StyleSheet.create({
  container: {height: 110},
  row: {flex: 1, flexDirection: 'row'},
  image: {width: 140, height: 98, borderRadius: 4, marginRight: 16},
  flex: {flex: 1},
  statusRow: {flex: 1, flexDirection: 'row', justifyContent: 'space-between'},
  statusTag: {height: 20, marginRight: 4},
  statusFirst: {width: 41},
  statusSecond: {width: 50},
  statusThird: {width: 46},
  checkbox: {width: 20, height: 20},
  title: {height: 36, marginTop: 24},
  price: {height: 18, marginTop: space['4xs']},
  likeViewWrap: {flexDirection: 'row', marginTop: space['4xs']},
  likeView: {width: 42, height: 12, marginRight: 4},
})
