import {View, StyleSheet, ViewProps} from 'react-native'
import React from 'react'
import {Placeholder, PlaceholderLine, Fade} from 'rn-placeholder'
import {colors} from '@liberty-ui-kit/theme'

export interface ServiceSelectionSkeletonProps extends ViewProps {}

const SKELETON_COLOR = colors.background // FIXME: Change this color

export const ServiceSelectionSkeleton = (
  props: ServiceSelectionSkeletonProps
) => {
  return (
    <View style={style.container} {...props}>
      <Placeholder Animation={Fade}>
        <View style={style.row}>
          <PlaceholderLine
            noMargin
            style={style.heading}
            color={SKELETON_COLOR}
          />
          <PlaceholderLine
            noMargin
            style={style.checkbox}
            color={SKELETON_COLOR}
          />
        </View>
        <View style={style.cardRow}>
          <PlaceholderLine
            noMargin
            style={style.image}
            color={SKELETON_COLOR}
          />
          <View style={style.info}>
            <PlaceholderLine
              noMargin
              style={style.title}
              color={SK<PERSON>ETON_COLOR}
            />
            <PlaceholderLine
              noMargin
              style={style.price}
              color={SKELETON_COLOR}
            />
          </View>
        </View>
      </Placeholder>
    </View>
  )
}

const style = StyleSheet.create({
  container: {height: 162},
  heading: {flex: 1, height: 20},
  row: {flex: 1, flexDirection: 'row'},
  cardRow: {flex: 1, flexDirection: 'row', marginTop: 32},
  image: {width: 140, height: 98, borderRadius: 4, marginRight: 16},
  checkbox: {width: 20, height: 20, marginLeft: 44},
  title: {height: 36},
  price: {maxWidth: 89, height: 24, marginTop: 22},
  info: {flex: 1, marginTop: 8},
})
