PODS:
  - abseil/algorithm (1.20220623.0):
    - abseil/algorithm/algorithm (= 1.20220623.0)
    - abseil/algorithm/container (= 1.20220623.0)
  - abseil/algorithm/algorithm (1.20220623.0):
    - abseil/base/config
  - abseil/algorithm/container (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base (1.20220623.0):
    - abseil/base/atomic_hook (= 1.20220623.0)
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/base_internal (= 1.20220623.0)
    - abseil/base/config (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/base/dynamic_annotations (= 1.20220623.0)
    - abseil/base/endian (= 1.20220623.0)
    - abseil/base/errno_saver (= 1.20220623.0)
    - abseil/base/fast_type_id (= 1.20220623.0)
    - abseil/base/log_severity (= 1.20220623.0)
    - abseil/base/malloc_internal (= 1.20220623.0)
    - abseil/base/prefetch (= 1.20220623.0)
    - abseil/base/pretty_function (= 1.20220623.0)
    - abseil/base/raw_logging_internal (= 1.20220623.0)
    - abseil/base/spinlock_wait (= 1.20220623.0)
    - abseil/base/strerror (= 1.20220623.0)
    - abseil/base/throw_delegate (= 1.20220623.0)
  - abseil/base/atomic_hook (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/config (1.20220623.0)
  - abseil/base/core_headers (1.20220623.0):
    - abseil/base/config
  - abseil/base/dynamic_annotations (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/endian (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/errno_saver (1.20220623.0):
    - abseil/base/config
  - abseil/base/fast_type_id (1.20220623.0):
    - abseil/base/config
  - abseil/base/log_severity (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/prefetch (1.20220623.0):
    - abseil/base/config
  - abseil/base/pretty_function (1.20220623.0)
  - abseil/base/raw_logging_internal (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/strerror (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/cleanup/cleanup (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
  - abseil/cleanup/cleanup_internal (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
  - abseil/container/common (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/compressed_tuple (1.20220623.0):
    - abseil/utility/utility
  - abseil/container/container_memory (1.20220623.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/container/fixed_array (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (1.20220623.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/flat_hash_set (1.20220623.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (1.20220623.0):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (1.20220623.0):
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug_hooks (1.20220623.0):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/container/inlined_vector (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
  - abseil/container/inlined_vector_internal (1.20220623.0):
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/raw_hash_map (1.20220623.0):
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
  - abseil/debugging/debugging_internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/stacktrace (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
  - abseil/functional/any_invocable (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/bind_front (1.20220623.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/function_ref (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/hash/city (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/hash/low_level_hash (1.20220623.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/memory (1.20220623.0):
    - abseil/memory/memory (= 1.20220623.0)
  - abseil/memory/memory (1.20220623.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (1.20220623.0):
    - abseil/meta/type_traits (= 1.20220623.0)
  - abseil/meta/type_traits (1.20220623.0):
    - abseil/base/config
  - abseil/numeric/bits (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/int128 (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
  - abseil/numeric/representation (1.20220623.0):
    - abseil/base/config
  - abseil/profiling/exponential_biased (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/profiling/sample_recorder (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
  - abseil/random/distributions (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
  - abseil/random/internal/distribution_caller (1.20220623.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
  - abseil/random/internal/fast_uniform_bits (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
  - abseil/random/internal/fastmath (1.20220623.0):
    - abseil/numeric/bits
  - abseil/random/internal/generate_real (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
  - abseil/random/internal/iostream_state_saver (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
  - abseil/random/internal/nonsecure_base (1.20220623.0):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
  - abseil/random/internal/pcg_engine (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
  - abseil/random/internal/platform (1.20220623.0):
    - abseil/base/config
  - abseil/random/internal/pool_urbg (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/random/internal/randen (1.20220623.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
  - abseil/random/internal/randen_engine (1.20220623.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
  - abseil/random/internal/randen_hwaes (1.20220623.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
  - abseil/random/internal/randen_hwaes_impl (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/randen_slow (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/salted_seed_seq (1.20220623.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/seed_material (1.20220623.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/traits (1.20220623.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/random/internal/uniform_helper (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/internal/wide_multiply (1.20220623.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/random (1.20220623.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
  - abseil/random/seed_gen_exception (1.20220623.0):
    - abseil/base/config
  - abseil/random/seed_sequences (1.20220623.0):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/status/status (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/status/statusor (1.20220623.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/strings/cord (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/strings/cord_internal (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/cordz_functions (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
  - abseil/strings/cordz_handle (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
  - abseil/strings/cordz_info (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/types/span
  - abseil/strings/cordz_statistics (1.20220623.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_scope (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_tracker (1.20220623.0):
    - abseil/base/config
  - abseil/strings/internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (1.20220623.0):
    - abseil/strings/str_format_internal
  - abseil/strings/str_format_internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
  - abseil/strings/strings (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/internal
  - abseil/synchronization/graphcycles_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (1.20220623.0):
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (1.20220623.0):
    - abseil/time/internal (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
  - abseil/time/internal (1.20220623.0):
    - abseil/time/internal/cctz (= 1.20220623.0)
  - abseil/time/internal/cctz (1.20220623.0):
    - abseil/time/internal/cctz/civil_time (= 1.20220623.0)
    - abseil/time/internal/cctz/time_zone (= 1.20220623.0)
  - abseil/time/internal/cctz/civil_time (1.20220623.0):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (1.20220623.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (1.20220623.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
  - abseil/types (1.20220623.0):
    - abseil/types/any (= 1.20220623.0)
    - abseil/types/bad_any_cast (= 1.20220623.0)
    - abseil/types/bad_any_cast_impl (= 1.20220623.0)
    - abseil/types/bad_optional_access (= 1.20220623.0)
    - abseil/types/bad_variant_access (= 1.20220623.0)
    - abseil/types/compare (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
  - abseil/types/any (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (1.20220623.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (1.20220623.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility/utility (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppsFlyerFramework (6.12.2):
    - AppsFlyerFramework/Main (= 6.12.2)
  - AppsFlyerFramework/Main (6.12.2)
  - boost (1.84.0)
  - BoringSSL-GRPC (0.0.24):
    - BoringSSL-GRPC/Implementation (= 0.0.24)
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Implementation (0.0.24):
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Interface (0.0.24)
  - BranchSDK (3.2.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - Crisp (2.2.1):
    - Crisp/Crisp (= 2.2.1)
  - Crisp/Crisp (2.2.1)
  - CropViewController (2.6.1)
  - DoubleConversion (1.1.6)
  - FBAEMKit (16.1.3):
    - FBSDKCoreKit_Basics (= 16.1.3)
  - FBLazyVector (0.75.4)
  - FBSDKCoreKit (16.1.3):
    - FBAEMKit (= 16.1.3)
    - FBSDKCoreKit_Basics (= 16.1.3)
  - FBSDKCoreKit_Basics (16.1.3)
  - FBSDKGamingServicesKit (16.1.3):
    - FBSDKCoreKit (= 16.1.3)
    - FBSDKCoreKit_Basics (= 16.1.3)
    - FBSDKShareKit (= 16.1.3)
  - FBSDKLoginKit (16.1.3):
    - FBSDKCoreKit (= 16.1.3)
  - FBSDKShareKit (16.1.3):
    - FBSDKCoreKit (= 16.1.3)
  - Firebase (10.14.0):
    - Firebase/Core (= 10.14.0)
  - Firebase/Analytics (10.14.0):
    - Firebase/Core
  - Firebase/Auth (10.14.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.14.0)
  - Firebase/Core (10.14.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.14.0)
  - Firebase/CoreOnly (10.14.0):
    - FirebaseCore (= 10.14.0)
  - Firebase/Firestore (10.14.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.14.0)
  - Firebase/Messaging (10.14.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.14.0)
  - FirebaseAnalytics (10.14.0):
    - FirebaseAnalytics/AdIdSupport (= 10.14.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.14.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.14.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.14.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.14.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.14.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseFirestore (10.14.0):
    - abseil/algorithm (~> 1.20220623.0)
    - abseil/base (~> 1.20220623.0)
    - abseil/container/flat_hash_map (~> 1.20220623.0)
    - abseil/memory (~> 1.20220623.0)
    - abseil/meta (~> 1.20220623.0)
    - abseil/strings/strings (~> 1.20220623.0)
    - abseil/time (~> 1.20220623.0)
    - abseil/types (~> 1.20220623.0)
    - FirebaseCore (~> 10.0)
    - "gRPC-C++ (~> 1.50.1)"
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.14.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - fmt (9.1.0)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.14.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.14.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.14.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.14.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.14.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.50.1)":
    - "gRPC-C++/Implementation (= 1.50.1)"
    - "gRPC-C++/Interface (= 1.50.1)"
  - "gRPC-C++/Implementation (1.50.1)":
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/cleanup/cleanup (= 1.20220623.0)
    - abseil/container/flat_hash_map (= 1.20220623.0)
    - abseil/container/flat_hash_set (= 1.20220623.0)
    - abseil/container/inlined_vector (= 1.20220623.0)
    - abseil/functional/any_invocable (= 1.20220623.0)
    - abseil/functional/bind_front (= 1.20220623.0)
    - abseil/functional/function_ref (= 1.20220623.0)
    - abseil/hash/hash (= 1.20220623.0)
    - abseil/memory/memory (= 1.20220623.0)
    - abseil/meta/type_traits (= 1.20220623.0)
    - abseil/random/random (= 1.20220623.0)
    - abseil/status/status (= 1.20220623.0)
    - abseil/status/statusor (= 1.20220623.0)
    - abseil/strings/cord (= 1.20220623.0)
    - abseil/strings/str_format (= 1.20220623.0)
    - abseil/strings/strings (= 1.20220623.0)
    - abseil/synchronization/synchronization (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
    - abseil/utility/utility (= 1.20220623.0)
    - "gRPC-C++/Interface (= 1.50.1)"
    - gRPC-Core (= 1.50.1)
  - "gRPC-C++/Interface (1.50.1)"
  - gRPC-Core (1.50.1):
    - gRPC-Core/Implementation (= 1.50.1)
    - gRPC-Core/Interface (= 1.50.1)
  - gRPC-Core/Implementation (1.50.1):
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/container/flat_hash_map (= 1.20220623.0)
    - abseil/container/flat_hash_set (= 1.20220623.0)
    - abseil/container/inlined_vector (= 1.20220623.0)
    - abseil/functional/any_invocable (= 1.20220623.0)
    - abseil/functional/bind_front (= 1.20220623.0)
    - abseil/functional/function_ref (= 1.20220623.0)
    - abseil/hash/hash (= 1.20220623.0)
    - abseil/memory/memory (= 1.20220623.0)
    - abseil/meta/type_traits (= 1.20220623.0)
    - abseil/random/random (= 1.20220623.0)
    - abseil/status/status (= 1.20220623.0)
    - abseil/status/statusor (= 1.20220623.0)
    - abseil/strings/cord (= 1.20220623.0)
    - abseil/strings/str_format (= 1.20220623.0)
    - abseil/strings/strings (= 1.20220623.0)
    - abseil/synchronization/synchronization (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
    - abseil/utility/utility (= 1.20220623.0)
    - BoringSSL-GRPC (= 0.0.24)
    - gRPC-Core/Interface (= 1.50.1)
  - gRPC-Core/Interface (1.50.1)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - hermes-engine (0.75.4):
    - hermes-engine/Pre-built (= 0.75.4)
  - hermes-engine/Pre-built (0.75.4)
  - leveldb-library (1.22.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.4.1)
  - lottie-react-native (6.7.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - lottie-ios (= 4.4.1)
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - MMKV (2.2.2):
    - MMKVCore (~> 2.2.2)
  - MMKVCore (2.2.2)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Default (= 2024.01.01.00)
  - RCT-Folly/Default (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCT-Folly/Fabric (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCTDeprecation (0.75.4)
  - RCTRequired (0.75.4)
  - RCTTypeSafety (0.75.4):
    - FBLazyVector (= 0.75.4)
    - RCTRequired (= 0.75.4)
    - React-Core (= 0.75.4)
  - React (0.75.4):
    - React-Core (= 0.75.4)
    - React-Core/DevSupport (= 0.75.4)
    - React-Core/RCTWebSocket (= 0.75.4)
    - React-RCTActionSheet (= 0.75.4)
    - React-RCTAnimation (= 0.75.4)
    - React-RCTBlob (= 0.75.4)
    - React-RCTImage (= 0.75.4)
    - React-RCTLinking (= 0.75.4)
    - React-RCTNetwork (= 0.75.4)
    - React-RCTSettings (= 0.75.4)
    - React-RCTText (= 0.75.4)
    - React-RCTVibration (= 0.75.4)
  - React-callinvoker (0.75.4)
  - React-Core (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.75.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/CoreModulesHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/Default (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/DevSupport (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.75.4)
    - React-Core/RCTWebSocket (= 0.75.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTBlobHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTImageHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTTextHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTWebSocket (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.75.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-CoreModules (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety (= 0.75.4)
    - React-Core/CoreModulesHeaders (= 0.75.4)
    - React-jsi (= 0.75.4)
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.75.4)
    - ReactCodegen
    - ReactCommon
    - SocketRocket (= 0.7.0)
  - React-cxxreact (0.75.4):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.75.4)
    - React-debug (= 0.75.4)
    - React-jsi (= 0.75.4)
    - React-jsinspector
    - React-logger (= 0.75.4)
    - React-perflogger (= 0.75.4)
    - React-runtimeexecutor (= 0.75.4)
  - React-debug (0.75.4)
  - React-defaultsnativemodule (0.75.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-domnativemodule
    - React-Fabric
    - React-featureflags
    - React-featureflagsnativemodule
    - React-graphics
    - React-idlecallbacksnativemodule
    - React-ImageManager
    - React-microtasksnativemodule
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-domnativemodule (0.75.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.75.4)
    - React-Fabric/attributedstring (= 0.75.4)
    - React-Fabric/componentregistry (= 0.75.4)
    - React-Fabric/componentregistrynative (= 0.75.4)
    - React-Fabric/components (= 0.75.4)
    - React-Fabric/core (= 0.75.4)
    - React-Fabric/dom (= 0.75.4)
    - React-Fabric/imagemanager (= 0.75.4)
    - React-Fabric/leakchecker (= 0.75.4)
    - React-Fabric/mounting (= 0.75.4)
    - React-Fabric/observers (= 0.75.4)
    - React-Fabric/scheduler (= 0.75.4)
    - React-Fabric/telemetry (= 0.75.4)
    - React-Fabric/templateprocessor (= 0.75.4)
    - React-Fabric/uimanager (= 0.75.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.75.4)
    - React-Fabric/components/root (= 0.75.4)
    - React-Fabric/components/view (= 0.75.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.75.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.75.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.75.4)
    - React-FabricComponents/textlayoutmanager (= 0.75.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.75.4)
    - React-FabricComponents/components/iostextinput (= 0.75.4)
    - React-FabricComponents/components/modal (= 0.75.4)
    - React-FabricComponents/components/rncore (= 0.75.4)
    - React-FabricComponents/components/safeareaview (= 0.75.4)
    - React-FabricComponents/components/scrollview (= 0.75.4)
    - React-FabricComponents/components/text (= 0.75.4)
    - React-FabricComponents/components/textinput (= 0.75.4)
    - React-FabricComponents/components/unimplementedview (= 0.75.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired (= 0.75.4)
    - RCTTypeSafety (= 0.75.4)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.75.4)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.75.4)
  - React-featureflagsnativemodule (0.75.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-graphics (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.75.4)
    - React-jsi
    - React-jsiexecutor (= 0.75.4)
    - React-jsinspector
    - React-perflogger (= 0.75.4)
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.75.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-ImageManager (0.75.4):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.75.4):
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-debug
    - React-jsi
  - React-jsi (0.75.4):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
  - React-jsiexecutor (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.75.4)
    - React-jsi (= 0.75.4)
    - React-jsinspector
    - React-perflogger (= 0.75.4)
  - React-jsinspector (0.75.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-featureflags
    - React-jsi
    - React-runtimeexecutor (= 0.75.4)
  - React-jsitracing (0.75.4):
    - React-jsi
  - React-logger (0.75.4):
    - glog
  - React-Mapbuffer (0.75.4):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.75.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-appsflyer (6.12.2):
    - AppsFlyerFramework (= 6.12.2)
    - React
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-branch (6.1.0):
    - BranchSDK (= 3.2.0)
    - React-Core
  - react-native-compressor (1.9.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-crisp-chat-sdk (0.13.4):
    - Crisp (= 2.2.1)
    - React
  - react-native-date-picker (4.2.14):
    - React-Core
  - react-native-fbsdk-next (12.1.2):
    - React-Core
    - react-native-fbsdk-next/Core (= 12.1.2)
    - react-native-fbsdk-next/Login (= 12.1.2)
    - react-native-fbsdk-next/Share (= 12.1.2)
  - react-native-fbsdk-next/Core (12.1.2):
    - FBSDKCoreKit (~> 16.1.3)
    - React-Core
  - react-native-fbsdk-next/Login (12.1.2):
    - FBSDKLoginKit (~> 16.1.3)
    - React-Core
  - react-native-fbsdk-next/Share (12.1.2):
    - FBSDKGamingServicesKit (~> 16.1.3)
    - FBSDKShareKit (~> 16.1.3)
    - React-Core
  - react-native-geolocation (3.1.0):
    - React-Core
  - react-native-keyboard-controller (1.14.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-mmkv (2.11.0):
    - MMKV (>= 1.2.13)
    - React-Core
  - react-native-multiple-image-picker (1.1.11):
    - CropViewController (= 2.6.1)
    - React-Core
    - react-native-multiple-image-picker/Viewer (= 1.1.11)
    - TLPhotoPicker (= 2.1.9)
  - react-native-multiple-image-picker/Viewer (1.1.11):
    - CropViewController (= 2.6.1)
    - React-Core
    - TLPhotoPicker (= 2.1.9)
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-pager-view (6.5.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.14.0):
    - React-Core
  - react-native-simple-heic2jpg (0.1.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-video (6.0.0-beta.5):
    - React-Core
    - react-native-video/Video (= 6.0.0-beta.5)
  - react-native-video/Video (6.0.0-beta.5):
    - PromisesSwift
    - React-Core
  - react-native-webview (13.12.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.75.4)
  - React-NativeModulesApple (0.75.4):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.75.4)
  - React-performancetimeline (0.75.4):
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact
  - React-RCTActionSheet (0.75.4):
    - React-Core/RCTActionSheetHeaders (= 0.75.4)
  - React-RCTAnimation (0.75.4):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTAppDelegate (0.75.4):
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon
  - React-RCTBlob (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTFabric (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.75.4):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTLinking (0.75.4):
    - React-Core/RCTLinkingHeaders (= 0.75.4)
    - React-jsi (= 0.75.4)
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.75.4)
  - React-RCTNetwork (0.75.4):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTSettings (0.75.4):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTText (0.75.4):
    - React-Core/RCTTextHeaders (= 0.75.4)
    - Yoga
  - React-RCTVibration (0.75.4):
    - RCT-Folly (= 2024.01.01.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-rendererconsistency (0.75.4)
  - React-rendererdebug (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
  - React-rncore (0.75.4)
  - React-RuntimeApple (0.75.4):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.75.4):
    - React-jsi (= 0.75.4)
  - React-RuntimeHermes (0.75.4):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.75.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
    - React-jsi (= 0.75.4)
  - ReactCodegen (0.75.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.75.4):
    - ReactCommon/turbomodule (= 0.75.4)
  - ReactCommon/turbomodule (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.75.4)
    - React-cxxreact (= 0.75.4)
    - React-jsi (= 0.75.4)
    - React-logger (= 0.75.4)
    - React-perflogger (= 0.75.4)
    - ReactCommon/turbomodule/bridging (= 0.75.4)
    - ReactCommon/turbomodule/core (= 0.75.4)
  - ReactCommon/turbomodule/bridging (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.75.4)
    - React-cxxreact (= 0.75.4)
    - React-jsi (= 0.75.4)
    - React-logger (= 0.75.4)
    - React-perflogger (= 0.75.4)
  - ReactCommon/turbomodule/core (0.75.4):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.75.4)
    - React-cxxreact (= 0.75.4)
    - React-debug (= 0.75.4)
    - React-featureflags (= 0.75.4)
    - React-jsi (= 0.75.4)
    - React-logger (= 0.75.4)
    - React-perflogger (= 0.75.4)
    - React-utils (= 0.75.4)
  - RecaptchaInterop (100.0.0)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNAppleAuthentication (2.3.0):
    - React-Core
  - RNCAsyncStorage (2.1.0):
    - React-Core
  - RNCClipboard (1.15.0):
    - React-Core
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNDeviceInfo (10.12.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBAnalytics (18.3.1):
    - Firebase/Analytics (= 10.14.0)
    - React-Core
    - RNFBApp
  - RNFBApp (18.3.1):
    - Firebase/CoreOnly (= 10.14.0)
    - React-Core
  - RNFBAuth (18.3.1):
    - Firebase/Auth (= 10.14.0)
    - React-Core
    - RNFBApp
  - RNFBFirestore (18.3.1):
    - Firebase/Firestore (= 10.14.0)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (18.3.1):
    - Firebase/Messaging (= 10.14.0)
    - FirebaseCoreExtension (= 10.14.0)
    - React-Core
    - RNFBApp
  - RNFlashList (1.6.3):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.14.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNGoogleSignin (13.1.0):
    - GoogleSignIn (~> 7.1)
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReactNativeHapticFeedback (2.2.0):
    - React-Core
  - RNReanimated (3.15.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.15.3)
    - RNReanimated/worklets (= 3.15.3)
    - Yoga
  - RNReanimated/reanimated (3.15.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.15.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.35.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNShare (11.1.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNStaticSafeAreaInsets (2.2.0):
    - React-Core
  - RNSVG (15.9.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.7.0)
  - TLPhotoPicker (2.1.9)
  - VisionCamera (4.5.0):
    - VisionCamera/Core (= 4.5.0)
    - VisionCamera/React (= 4.5.0)
  - VisionCamera/Core (4.5.0)
  - VisionCamera/React (4.5.0):
    - React-Core
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - Firebase
  - FirebaseCore
  - FirebaseCoreInternal
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - GTMSessionFetcher
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-appsflyer (from `../node_modules/react-native-appsflyer`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - react-native-branch (from `../node_modules/react-native-branch`)
  - react-native-compressor (from `../node_modules/react-native-compressor`)
  - react-native-crisp-chat-sdk (from `../node_modules/react-native-crisp-chat-sdk`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-fbsdk-next (from `../node_modules/react-native-fbsdk-next`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-keyboard-controller (from `../node_modules/react-native-keyboard-controller`)
  - react-native-mmkv (from `../node_modules/react-native-mmkv`)
  - "react-native-multiple-image-picker (from `../node_modules/@nnpmai/react-native-multiple-image-picker`)"
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-simple-heic2jpg (from `../node_modules/react-native-simple-heic2jpg`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - "RNFBFirestore (from `../node_modules/@react-native-firebase/firestore`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReactNativeHapticFeedback (from `../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNStaticSafeAreaInsets (from `../node_modules/react-native-static-safe-area-insets`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - AppsFlyerFramework
    - BoringSSL-GRPC
    - BranchSDK
    - Crisp
    - CropViewController
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKGamingServicesKit
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - libwebp
    - lottie-ios
    - MMKV
    - MMKVCore
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - TLPhotoPicker

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-08-15-RNv0.75.1-4b3bf912cc0f705b51b71ce1a5b8bd79b93a451b
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-appsflyer:
    :path: "../node_modules/react-native-appsflyer"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-branch:
    :path: "../node_modules/react-native-branch"
  react-native-compressor:
    :path: "../node_modules/react-native-compressor"
  react-native-crisp-chat-sdk:
    :path: "../node_modules/react-native-crisp-chat-sdk"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-fbsdk-next:
    :path: "../node_modules/react-native-fbsdk-next"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-keyboard-controller:
    :path: "../node_modules/react-native-keyboard-controller"
  react-native-mmkv:
    :path: "../node_modules/react-native-mmkv"
  react-native-multiple-image-picker:
    :path: "../node_modules/@nnpmai/react-native-multiple-image-picker"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-simple-heic2jpg:
    :path: "../node_modules/react-native-simple-heic2jpg"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNFBFirestore:
    :path: "../node_modules/@react-native-firebase/firestore"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReactNativeHapticFeedback:
    :path: "../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNStaticSafeAreaInsets:
    :path: "../node_modules/react-native-static-safe-area-insets"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  abseil: 926fb7a82dc6d2b8e1f2ed7f3a718bce691d1e46
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppsFlyerFramework: 6eb4d89d2eb9a6632317f1055b359d9fd85fd5ff
  boost: 4cb898d0bf20404aab1850c656dcea009429d6c1
  BoringSSL-GRPC: 3175b25143e648463a56daeaaa499c6cb86dad33
  BranchSDK: ffa2a71be00efb4cd6e54b55f46349750a5a7625
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  Crisp: 7596490beb8c0d23e41c5ade5d51b7937a5a589c
  CropViewController: 58fb440f30dac788b129d2a1f24cffdcb102669c
  DoubleConversion: 76ab83afb40bddeeee456813d9c04f67f78771b5
  FBAEMKit: af2972f39bb0f3f7c45998f435b007833c32ffb2
  FBLazyVector: 430e10366de01d1e3d57374500b1b150fe482e6d
  FBSDKCoreKit: 19e2e18b3be578d7a51fed8fdd8c152bef0b9511
  FBSDKCoreKit_Basics: dd9826ce3c9fd9f8cdf8dbbd0ef0a53e6c0c9e7e
  FBSDKGamingServicesKit: 791d9b1de6da359e92da3b1ba3459f36b0aa604f
  FBSDKLoginKit: c395c63a1a6cf4a8a1e6103fd94b8c46329ee81c
  FBSDKShareKit: de5c291ad414ec07048ab09bae1b1b74b3ed92fe
  Firebase: 6c1bf3f534bc422d52af2e41fe0d50bf08b6b773
  FirebaseAnalytics: 5c6d58814afa4db82cf7fdbc02b0b0e2fa3d43ff
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: bd1ae3d28beb83bfe9247e50eb82688b683dd770
  FirebaseCore: 6fc17ac9f03509d51c131298aacb3ee5698b4f02
  FirebaseCoreExtension: 976638051b1a46b503afce7ec80277f9161f2040
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseFirestore: 808dd08cbc1c7be9052055f62ab312fdae611e37
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 1077a4499f0c0a140b9a2e34fe08a1acc806b36d
  fmt: 4c2741a687cc09f0634a2e2c72a838b99f1ff120
  glog: 69ef571f3de08433d766d614c73a9838a06bf7eb
  GoogleAppMeasurement: 7fee012a868315d418f365fbc8d394d8e020e749
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  "gRPC-C++": 0968bace703459fd3e5dcb0b2bed4c573dbff046
  gRPC-Core: 17108291d84332196d3c8466b48f016fc17d816d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  hermes-engine: ea92f60f37dba025e293cbe4b4a548fd26b610a0
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: e047b1d2e6239b787cc5e9755b988869cf190494
  lottie-react-native: 5b9cfa4080058d83f8e8ca442704b9e5d5b81a7a
  MMKV: b4802ebd5a7c68fc0c4a5ccb4926fbdfb62d68e0
  MMKVCore: a255341a3746955f50da2ad9121b18cb2b346e61
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 34124ae2e667a0e5f0ea378db071d27548124321
  RCTDeprecation: 726d24248aeab6d7180dac71a936bbca6a994ed1
  RCTRequired: a94e7febda6db0345d207e854323c37e3a31d93b
  RCTTypeSafety: 28e24a6e44f5cbf912c66dde6ab7e07d1059a205
  React: c2830fa483b0334bda284e46a8579ebbe0c5447e
  React-callinvoker: 4aecde929540c26b841a4493f70ebf6016691eb8
  React-Core: 32a581847d74ce9b5f51d9d11a4e4d132ad61553
  React-CoreModules: f53e0674e1747fa41c83bc970e82add97b14ad87
  React-cxxreact: 86f3b1692081fd954a0cb27cc90d14674645b64b
  React-debug: 3d21f69d8def0656f8b8ec25c0f05954f4d862c5
  React-defaultsnativemodule: 2ed121c5a1edeab09cff382b8d9b538260f07848
  React-domnativemodule: 4393dd5dd7e13dbe42e69ebc791064a616990f91
  React-Fabric: cbf38ceefb1ac6236897abdb538130228e126695
  React-FabricComponents: dd4b01c4a60920d8dc15f3b5594c6fe9e7648a38
  React-FabricImage: 8b13aedfbd20f349b9b3314baf993c71c02995d9
  React-featureflags: ee1abd6f71555604a36cda6476e3c502ca9a48e5
  React-featureflagsnativemodule: 87b58caf3cd8eca1e53179453789def019af2a65
  React-graphics: f5c4cf3abc5aa083e28fe7a866bd95fb3bbbc1e0
  React-hermes: cad69ee9a53870cc38e5386889aa7ea81c75b6a1
  React-idlecallbacksnativemodule: 445390be0f533797ace18c419eb57110dbfe90d6
  React-ImageManager: cb78d7a24f45f8f9a5a1640b52fce4c9f637f98d
  React-jserrorhandler: dfe9b96e99a93d4f4858bad66d5bc4813a87a21a
  React-jsi: bc1f6073e203fb540edd6d26f926ad041809b443
  React-jsiexecutor: 1e8fc70dd9614c3e9d5c3c876b2ea3cd1d931ee4
  React-jsinspector: 7544a20e9beac390f1b65d9f0040d97cd55dc198
  React-jsitracing: cac972ccc097db399df8044e49add8e5b25cb34a
  React-logger: 80d87daf2f98bf95ab668b79062c1e0c3f0c2f8a
  React-Mapbuffer: acffb35a53a5f474ede09f082ac609b41aafab2e
  React-microtasksnativemodule: 71ca9282bce93b319218d75362c0d646b376eb43
  react-native-appsflyer: c99f0b95e4a2114987338d65a5fd7a273a02f915
  react-native-background-timer: 4638ae3bee00320753647900b21260b10587b6f7
  react-native-branch: 605b3fe8283248ff15b3a78f79f2137b9f8f5b57
  react-native-compressor: 5c3b47b045a411a86b4b07c9d220933530069372
  react-native-crisp-chat-sdk: 0a40af0bb576da468b6eb4d02ff7473ebce5fdc7
  react-native-date-picker: e8dec4d656e43d06df9e3d230ad3cc9370e1758d
  react-native-fbsdk-next: 3f8a31058a0a77c0c151b7c2a865dcabc2f82a1b
  react-native-geolocation: f0fa51740690633a1adb6222539166b484facefb
  react-native-keyboard-controller: c73451e032fa21f0f13e1143e0dae01d865cb387
  react-native-mmkv: 5a46c73e3e12aa872c4485ae0e4414b4040af79a
  react-native-multiple-image-picker: d431065e060a5c8e5b76efe549fcf4a431065032
  react-native-netinfo: cec9c4e86083cb5b6aba0e0711f563e2fbbff187
  react-native-pager-view: a58a82591f421322e2bdb4fb69b82f6486dd4b22
  react-native-render-html: 5afc4751f1a98621b3009432ef84c47019dcb2bd
  react-native-safe-area-context: b13be9714d9771fbde0120bc519c963484de3a71
  react-native-simple-heic2jpg: b5122795f9402a21c1d9c0035094943a991094e6
  react-native-splash-screen: 95994222cc95c236bd3cdc59fe45ed5f27969594
  react-native-video: 9ae0990f7ee5787e6e2f5b3b3400175e33ff7520
  react-native-webview: fa8441a7333fae80953a0be70ad568d80430a273
  React-nativeconfig: 8c83d992b9cc7d75b5abe262069eaeea4349f794
  React-NativeModulesApple: 97f606f09fd9840b3868333984d6a0e7bcc425b5
  React-perflogger: 59e1a3182dca2cee7b9f1f7aab204018d46d1914
  React-performancetimeline: 3e3f5c5576fe1cc2dd5fcfb1ae2046d5dceda3d7
  React-RCTActionSheet: d80e68d3baa163e4012a47c1f42ddd8bcd9672cc
  React-RCTAnimation: 051f0781709c5ed80ba8aa2b421dfb1d72a03162
  React-RCTAppDelegate: 106d225d076988b06aa4834e68d1ab754f40cacf
  React-RCTBlob: 895eaf8bca2e76ee1c95b479235c6ccebe586fc6
  React-RCTFabric: 8d01df202ee9e933f9b5dd44b72ec89a7ac6ee01
  React-RCTImage: b73149c0cd54b641dba2d6250aaf168fee784d9f
  React-RCTLinking: 23e519712285427e50372fbc6e0265d422abf462
  React-RCTNetwork: a5d06d122588031989115f293654b13353753630
  React-RCTSettings: 87d03b5d94e6eadd1e8c1d16a62f790751aafb55
  React-RCTText: 75e9dd39684f4bcd1836134ac2348efaca7437b3
  React-RCTVibration: 033c161fe875e6fa096d0d9733c2e2501682e3d4
  React-rendererconsistency: f620c6e003e3c4593e6349d8242b8aeb3d4633f0
  React-rendererdebug: 5be7b834677b2a7a263f4d2545f0d4966cafad82
  React-rncore: c22bd84cc2f38947f0414fab6646db22ff4f80cd
  React-RuntimeApple: 71160e6c02efa07d198b84ef5c3a52a7d9d0399d
  React-RuntimeCore: f88f79ec995c12af56a265d7505c7630733d9d82
  React-runtimeexecutor: ea90d8e3a9e0f4326939858dafc6ab17c031a5d3
  React-RuntimeHermes: 49f86328914021f50fd5a5b9756685f5f6d8b4da
  React-runtimescheduler: fed70991b942c6df752a59a22081e45fc811b11c
  React-utils: 02526ea15628a768b8db9517b6017a1785c734d2
  ReactCodegen: 8b5341ecb61898b8bd40a73ebc443c6bf2d14423
  ReactCommon: 36d48f542b4010786d6b2bcee615fe5f906b7105
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  rn-fetch-blob: 25612b6d6f6e980c6f17ed98ba2f58f5696a51ca
  RNAppleAuthentication: 445222cf501db405f0b3713593a6d0fecc87fd05
  RNCAsyncStorage: c91d753ede6dc21862c4922cd13f98f7cfde578e
  RNCClipboard: dbcf25b8f666b4685c02eeb65be981d30198e505
  RNCPushNotificationIOS: 6c4ca3388c7434e4a662b92e4dfeeee858e6f440
  RNDeviceInfo: addb9b427c2822a2d8e94c87a136a224e0af738c
  RNFastImage: 462a183c4b0b6b26fdfd639e1ed6ba37536c3b87
  RNFBAnalytics: 033d776bd6ed14bb7ca5a1b35de888dce80816a4
  RNFBApp: 7f4f4f2bf9167a78fdf03509237ec0fbbe481cb3
  RNFBAuth: 27f9232c8d53f479ac839749afc32ee689aac3de
  RNFBFirestore: 907583be851042f1730b1e9ea0e633e1fe5e0f42
  RNFBMessaging: 6828f97d36e1d4d6dd6d480d8ff12e5b5d43c8e1
  RNFlashList: 1076a3fb7c4608a8cdf265f0783592b8fc41b6a7
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: 47251a57875d5cf77890ff7948ca09529c2430e2
  RNGoogleSignin: ba93c1137f8d5cebdd39b04f493fd212ddf5ecd6
  RNPermissions: 539b9b510cc6c4bd11e868e021d2303c0fcc9459
  RNReactNativeHapticFeedback: a6fb5b7a981683bf58af43e3fb827d4b7ed87f83
  RNReanimated: 43afcc28696bc25a461170177c29bca21983a5ee
  RNScreens: 35bb8e81aeccf111baa0ea01a54231390dbbcfd9
  RNShare: 6204e6a1987ba3e7c47071ef703e5449a0e3548a
  RNStaticSafeAreaInsets: 4696b82d3a11ba6f3a790159ddb7290f04abd275
  RNSVG: bb4bfcb8ec723a6f34b074a1b7cd40ee35246fe5
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: abac6f5de4d4d62d24e11868d7a2f427e0ef940d
  TLPhotoPicker: 939c40eaad130b69f9571e61e021e8120fa76329
  VisionCamera: 499873729ea4f22e82b726428b546b0d2531ff37
  Yoga: 055f92ad73f8c8600a93f0e25ac0b2344c3b07e6

PODFILE CHECKSUM: 881a705a4756e270e6bba2eb06e1854331a1280c

COCOAPODS: 1.16.2
